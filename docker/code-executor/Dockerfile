# 完整多语言代码执行环境 Dockerfile
# 支持 Python, Java, C, C++, JavaScript (Node.js)
# 基于 Ubuntu 22.04 LTS - 使用清华大学镜像源优化

FROM ubuntu:22.04

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai
ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8
ENV PIP_BREAK_SYSTEM_PACKAGES=1

# 设置工作目录
WORKDIR /home/<USER>

# 替换为清华大学镜像源
RUN sed -i 's@//.*archive.ubuntu.com@//mirrors.tuna.tsinghua.edu.cn@g' /etc/apt/sources.list && \
    sed -i 's@//.*security.ubuntu.com@//mirrors.tuna.tsinghua.edu.cn@g' /etc/apt/sources.list

# 更新系统并安装基础工具
RUN apt-get update && apt-get install -y \
    # 基础工具
    curl \
    wget \
    git \
    vim \
    nano \
    unzip \
    zip \
    tree \
    htop \
    # 构建工具
    build-essential \
    cmake \
    make \
    # 网络工具
    net-tools \
    iputils-ping \
    # 其他实用工具
    software-properties-common \
    apt-transport-https \
    ca-certificates \
    gnupg \
    lsb-release \
    && rm -rf /var/lib/apt/lists/*

# 安装 Python 3.11 和相关工具（避免安装系统python包）
RUN apt-get update && apt-get install -y \
    python3.11 \
    python3.11-dev \
    python3.11-venv \
    python3-pip \
    && rm -rf /var/lib/apt/lists/*

# 创建 python3 软链接
RUN ln -sf /usr/bin/python3.11 /usr/bin/python3 && \
    ln -sf /usr/bin/python3.11 /usr/bin/python

# 创建虚拟环境避免系统包冲突
RUN python3 -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# 配置pip使用清华大学镜像源
RUN mkdir -p /root/.pip && \
    echo '[global]\nindex-url = https://pypi.tuna.tsinghua.edu.cn/simple\ntrusted-host = pypi.tuna.tsinghua.edu.cn\n[install]\ntrusted-host = pypi.tuna.tsinghua.edu.cn' > /root/.pip/pip.conf

# 升级 pip 并安装常用 Python 包
RUN pip install --upgrade pip setuptools wheel

# 分步安装Python包，避免网络超时和依赖冲突
# 基础包
RUN pip install --no-cache-dir -i https://pypi.tuna.tsinghua.edu.cn/simple \
    requests \
    urllib3 \
    certifi

# 开发工具
RUN pip install --no-cache-dir -i https://pypi.tuna.tsinghua.edu.cn/simple \
    pytest \
    black \
    flake8 \
    ipython

# Web框架
RUN pip install --no-cache-dir -i https://pypi.tuna.tsinghua.edu.cn/simple \
    flask \
    fastapi \
    uvicorn

# 网页解析
RUN pip install --no-cache-dir -i https://pypi.tuna.tsinghua.edu.cn/simple \
    beautifulsoup4 \
    lxml

# 科学计算包（使用清华源，如果失败则尝试豆瓣源）
RUN pip install --no-cache-dir -i https://pypi.tuna.tsinghua.edu.cn/simple numpy || \
    pip install --no-cache-dir -i https://pypi.douban.com/simple numpy || \
    echo "numpy安装失败，跳过"

RUN pip install --no-cache-dir -i https://pypi.tuna.tsinghua.edu.cn/simple pandas || \
    pip install --no-cache-dir -i https://pypi.douban.com/simple pandas || \
    echo "pandas安装失败，跳过"

RUN pip install --no-cache-dir -i https://pypi.tuna.tsinghua.edu.cn/simple matplotlib || \
    pip install --no-cache-dir -i https://pypi.douban.com/simple matplotlib || \
    echo "matplotlib安装失败，跳过"

RUN pip install --no-cache-dir -i https://pypi.tuna.tsinghua.edu.cn/simple seaborn || \
    pip install --no-cache-dir -i https://pypi.douban.com/simple seaborn || \
    echo "seaborn安装失败，跳过"

RUN pip install --no-cache-dir -i https://pypi.tuna.tsinghua.edu.cn/simple scipy || \
    pip install --no-cache-dir -i https://pypi.douban.com/simple scipy || \
    echo "scipy安装失败，跳过"

RUN pip install --no-cache-dir -i https://pypi.tuna.tsinghua.edu.cn/simple scikit-learn || \
    pip install --no-cache-dir -i https://pypi.douban.com/simple scikit-learn || \
    echo "scikit-learn安装失败，跳过"

# 图像处理
RUN pip install --no-cache-dir -i https://pypi.tuna.tsinghua.edu.cn/simple pillow || \
    pip install --no-cache-dir -i https://pypi.douban.com/simple pillow || \
    echo "pillow安装失败，跳过"

# Jupyter相关
RUN pip install --no-cache-dir -i https://pypi.tuna.tsinghua.edu.cn/simple jupyter || \
    pip install --no-cache-dir -i https://pypi.douban.com/simple jupyter || \
    echo "jupyter安装失败，跳过"

# 机器学习包（可选，如果网络允许）
RUN pip install --no-cache-dir -i https://pypi.tuna.tsinghua.edu.cn/simple tensorflow-cpu || \
    echo "TensorFlow安装失败，跳过"

RUN pip install --no-cache-dir -i https://pypi.tuna.tsinghua.edu.cn/simple torch torchvision --index-url https://download.pytorch.org/whl/cpu || \
    echo "PyTorch安装失败，跳过"

RUN pip install --no-cache-dir -i https://pypi.tuna.tsinghua.edu.cn/simple transformers || \
    echo "Transformers安装失败，跳过"

# 安装 Java 17 (OpenJDK)
RUN apt-get update && apt-get install -y \
    openjdk-17-jdk \
    openjdk-17-jre \
    maven \
    gradle \
    && rm -rf /var/lib/apt/lists/*

# 设置 Java 环境变量
ENV JAVA_HOME=/usr/lib/jvm/java-17-openjdk-amd64
ENV PATH=$PATH:$JAVA_HOME/bin

# 安装 C/C++ 编译器和相关工具
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    gdb \
    libc6-dev \
    libstdc++-12-dev \
    && rm -rf /var/lib/apt/lists/*

# 安装 Node.js 18.x 和 npm
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - && \
    apt-get install -y nodejs && \
    rm -rf /var/lib/apt/lists/*

# 配置npm使用淘宝镜像源
RUN npm config set registry https://registry.npmmirror.com

# 安装基础的 Node.js 包
RUN npm install -g \
    nodemon \
    jest \
    eslint \
    prettier || echo "部分npm包安装失败，继续执行"

# 创建执行用户（非root用户，提高安全性）
RUN useradd -m -s /bin/bash executor && \
    usermod -aG sudo executor && \
    echo "executor ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers

# 设置工作目录权限
RUN mkdir -p /home/<USER>
    chown -R executor:executor /home/<USER>
    chmod 755 /home/<USER>

# 创建临时目录
RUN mkdir -p /tmp/code-execution && \
    chown -R executor:executor /tmp/code-execution && \
    chmod 755 /tmp/code-execution

# 安装额外的开发工具
RUN apt-get update && apt-get install -y \
    # 版本控制
    git \
    # 文本处理
    jq \
    # 压缩工具
    zip \
    unzip \
    tar \
    gzip \
    # 系统监控
    htop \
    iotop \
    # 网络工具
    curl \
    wget \
    netcat \
    && rm -rf /var/lib/apt/lists/*

# 设置时区
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建启动脚本
RUN echo '#!/bin/bash\n\
echo "=== 多语言代码执行环境 (清华源优化) ==="\n\
echo "Python版本: $(python3 --version)"\n\
echo "Java版本: $(java -version 2>&1 | head -n 1)"\n\
echo "Node.js版本: $(node --version)"\n\
echo "GCC版本: $(gcc --version | head -n 1)"\n\
echo "G++版本: $(g++ --version | head -n 1)"\n\
echo "工作目录: $(pwd)"\n\
echo "用户: $(whoami)"\n\
echo "pip镜像源: 清华大学"\n\
echo "npm镜像源: 淘宝镜像"\n\
echo "=========================================="\n\
# 保持容器运行\n\
exec "$@"' > /home/<USER>/start.sh && \
    chmod +x /home/<USER>/start.sh

# 切换到执行用户
USER executor

# 为executor用户配置pip镜像源和虚拟环境
RUN mkdir -p ~/.pip && \
    echo '[global]\nindex-url = https://pypi.tuna.tsinghua.edu.cn/simple\ntrusted-host = pypi.tuna.tsinghua.edu.cn\n[install]\ntrusted-host = pypi.tuna.tsinghua.edu.cn' > ~/.pip/pip.conf

# 确保executor用户使用虚拟环境
ENV PATH="/opt/venv/bin:$PATH"

# 设置默认工作目录
WORKDIR /home/<USER>

# 验证安装
RUN python3 --version && \
    java -version && \
    node --version && \
    npm --version && \
    gcc --version && \
    g++ --version

# 默认命令
CMD ["/bin/bash", "/home/<USER>/start.sh", "tail", "-f", "/dev/null"]

# 标签信息
LABEL maintainer="ruoyi-team"
LABEL description="Multi-language code execution environment supporting Python, Java, C, C++, JavaScript"
LABEL version="1.0.0"
LABEL languages="python,java,c,cpp,javascript"

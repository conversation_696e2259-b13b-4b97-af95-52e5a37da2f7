#!/bin/bash

# 多语言代码执行环境 Docker 镜像构建脚本
# 支持 Python, Java, C, C++, JavaScript

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 镜像信息
IMAGE_NAME="multi-lang-executor"
IMAGE_TAG="latest"
FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"
DOCKERFILE="Dockerfile"

# 函数：打印彩色消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 函数：检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_message $RED "错误: Docker 未安装或不在 PATH 中"
        exit 1
    fi

    if ! docker info &> /dev/null; then
        print_message $RED "错误: Docker 服务未运行或当前用户无权限访问 Docker"
        exit 1
    fi

    print_message $GREEN "✓ Docker 环境检查通过"
}

# 函数：检查Dockerfile是否存在
check_dockerfile() {
    if [ ! -f "$DOCKERFILE" ]; then
        print_message $RED "错误: 当前目录下未找到 $DOCKERFILE"
        exit 1
    fi
    print_message $GREEN "✓ $DOCKERFILE 文件存在"
}

# 函数：显示构建信息
show_build_info() {
    print_message $BLUE "=== 完整多语言代码执行环境构建器 ==="
    echo "镜像名称: $FULL_IMAGE_NAME"
    echo "Dockerfile: $DOCKERFILE"
    echo "支持语言: Python 3.11, Java 17, C/C++ (GCC), JavaScript (Node.js 18)"
    echo "基础镜像: Ubuntu 22.04 LTS"
    echo "镜像源优化: 清华大学 + 淘宝镜像"
    echo "Python包: numpy, pandas, matplotlib, tensorflow, pytorch, transformers 等"
    echo "构建时间: $(date)"
    print_message $BLUE "============================================="
}

# 函数：构建Docker镜像
build_image() {
    print_message $YELLOW "开始构建 Docker 镜像..."

    # 构建镜像
    if docker build -f $DOCKERFILE -t $FULL_IMAGE_NAME .; then
        print_message $GREEN "✓ Docker 镜像构建成功: $FULL_IMAGE_NAME"
    else
        print_message $RED "✗ Docker 镜像构建失败"
        exit 1
    fi
}

# 函数：验证镜像
verify_image() {
    print_message $YELLOW "验证 Docker 镜像..."

    # 检查镜像是否存在
    if docker images | grep -q "$IMAGE_NAME.*$IMAGE_TAG"; then
        print_message $GREEN "✓ 镜像验证成功"

        # 显示镜像信息
        print_message $BLUE "镜像详细信息:"
        docker images | grep "$IMAGE_NAME.*$IMAGE_TAG"

        # 获取镜像大小
        IMAGE_SIZE=$(docker images --format "table {{.Size}}" | grep -A1 "SIZE" | tail -1)
        echo "镜像大小: $IMAGE_SIZE"

    else
        print_message $RED "✗ 镜像验证失败"
        exit 1
    fi
}

# 函数：测试镜像
test_image() {
    print_message $YELLOW "测试 Docker 镜像..."

    # 创建临时容器进行测试
    CONTAINER_ID=$(docker run -d $FULL_IMAGE_NAME)

    if [ $? -eq 0 ]; then
        print_message $GREEN "✓ 容器启动成功: $CONTAINER_ID"

        # 测试各种语言环境
        print_message $BLUE "测试语言环境:"

        # 测试 Python
        echo -n "Python: "
        docker exec $CONTAINER_ID python3 --version

        # 测试 Java
        echo -n "Java: "
        docker exec $CONTAINER_ID java -version 2>&1 | head -n 1

        # 测试 Node.js
        echo -n "Node.js: "
        docker exec $CONTAINER_ID node --version

        # 测试 GCC
        echo -n "GCC: "
        docker exec $CONTAINER_ID gcc --version | head -n 1

        # 测试 G++
        echo -n "G++: "
        docker exec $CONTAINER_ID g++ --version | head -n 1

        # 清理测试容器
        docker stop $CONTAINER_ID > /dev/null
        docker rm $CONTAINER_ID > /dev/null

        print_message $GREEN "✓ 语言环境测试完成，容器已清理"
    else
        print_message $RED "✗ 容器启动失败"
        exit 1
    fi
}

# 函数：显示使用说明
show_usage() {
    print_message $BLUE "=== 完整版使用说明 ==="
    echo "1. 启动容器:"
    echo "   docker run -d --name code-executor --memory=4g --cpus=2.0 $FULL_IMAGE_NAME"
    echo ""
    echo "2. 进入容器:"
    echo "   docker exec -it code-executor /bin/bash"
    echo ""
    echo "3. 测试Python科学计算:"
    echo "   python3 -c \"import numpy as np; print('NumPy:', np.__version__)\""
    echo "   python3 -c \"import pandas as pd; print('Pandas:', pd.__version__)\""
    echo "   python3 -c \"import matplotlib; print('Matplotlib:', matplotlib.__version__)\""
    echo ""
    echo "4. 测试机器学习库:"
    echo "   python3 -c \"import tensorflow as tf; print('TensorFlow:', tf.__version__)\" || echo 'TensorFlow未安装'"
    echo "   python3 -c \"import torch; print('PyTorch:', torch.__version__)\" || echo 'PyTorch未安装'"
    echo ""
    echo "5. 执行代码示例:"
    echo "   # Python数据分析"
    echo "   python3 -c \"import pandas as pd; df = pd.DataFrame({'A': [1,2,3], 'B': [4,5,6]}); print(df)\""
    echo ""
    echo "   # Java"
    echo "   echo 'public class Test { public static void main(String[] args) { System.out.println(\"Hello Java!\"); } }' > Test.java && javac Test.java && java Test"
    echo ""
    echo "   # C/C++"
    echo "   echo '#include <stdio.h>\nint main() { printf(\"Hello C!\\n\"); return 0; }' > test.c && gcc test.c -o test && ./test"
    echo ""
    echo "   # JavaScript"
    echo "   echo 'console.log(\"Hello JavaScript!\");' > test.js && node test.js"
    echo ""
    echo "6. Spring Boot应用配置:"
    echo "   docker.multi-lang.image: $FULL_IMAGE_NAME"
    print_message $BLUE "========================="
}



# 主函数
main() {
    print_message $GREEN "=== 完整多语言代码执行环境 Docker 镜像构建器 ==="
    print_message $YELLOW "此版本包含完整的Python科学计算包和机器学习库"

    # 检查环境
    check_docker
    check_dockerfile

    # 显示构建信息
    show_build_info

    # 询问是否继续
    read -p "是否继续构建完整版本? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_message $YELLOW "构建已取消"
        exit 0
    fi

    # 构建镜像
    build_image

    # 验证镜像
    verify_image

    # 测试镜像
    test_image

    # 显示使用说明
    show_usage

    print_message $GREEN "=== 构建完成 ==="
    print_message $GREEN "完整版镜像 $FULL_IMAGE_NAME 已成功构建并测试完成！"
    print_message $BLUE "包含的主要Python包: numpy, pandas, matplotlib, tensorflow, pytorch, transformers"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi

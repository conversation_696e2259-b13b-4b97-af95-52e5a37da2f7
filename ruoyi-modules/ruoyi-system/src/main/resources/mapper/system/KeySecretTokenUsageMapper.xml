<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.KeySecretTokenUsageMapper">

    <resultMap type="com.ruoyi.system.domain.KeySecretTokenUsage" id="KeySecretTokenUsageResult">
        <result property="id"    column="id"    />
        <result property="token"    column="token"    />
        <result property="totalPointCount"    column="total_point_count"    />
        <result property="pointUsage"    column="point_usage"    />
        <result property="totalCallCount"    column="total_call_count"    />
        <result property="lastAccessTime"    column="last_access_time"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectKeySecretTokenUsageVo">
        select id, token, total_point_count, point_usage, total_call_count, last_access_time, remark, status, create_by, create_time, update_by, update_time from s_key_secret_token_usage
    </sql>

    <select id="selectKeySecretTokenUsageList" parameterType="com.ruoyi.system.domain.KeySecretTokenUsage" resultMap="KeySecretTokenUsageResult">
        <include refid="selectKeySecretTokenUsageVo"/>
        <where>
            <if test="totalPointCount != null "> and total_point_count = #{totalPointCount}</if>
            <if test="pointUsage != null "> and point_usage = #{pointUsage}</if>
            <if test="totalCallCount != null "> and total_call_count = #{totalCallCount}</if>
            <if test="lastAccessTime != null "> and last_access_time = #{lastAccessTime}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>

    <select id="selectKeySecretTokenUsageById" parameterType="Long" resultMap="KeySecretTokenUsageResult">
        <include refid="selectKeySecretTokenUsageVo"/>
        where id = #{id}
    </select>

    <insert id="insertKeySecretTokenUsage" parameterType="com.ruoyi.system.domain.KeySecretTokenUsage" useGeneratedKeys="true" keyProperty="id">
        insert into s_key_secret_token_usage
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="token != null">token,</if>
            <if test="totalPointCount != null">total_point_count,</if>
            <if test="pointUsage != null">point_usage,</if>
            <if test="totalCallCount != null">total_call_count,</if>
            <if test="lastAccessTime != null">last_access_time,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="token != null">#{token},</if>
            <if test="totalPointCount != null">#{totalPointCount},</if>
            <if test="pointUsage != null">#{pointUsage},</if>
            <if test="totalCallCount != null">#{totalCallCount},</if>
            <if test="lastAccessTime != null">#{lastAccessTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateKeySecretTokenUsage" parameterType="com.ruoyi.system.domain.KeySecretTokenUsage">
        update s_key_secret_token_usage
        <trim prefix="SET" suffixOverrides=",">
            <if test="token != null">token = #{token},</if>
            <if test="totalPointCount != null">total_point_count = #{totalPointCount},</if>
            <if test="pointUsage != null">point_usage = #{pointUsage},</if>
            <if test="totalCallCount != null">total_call_count = #{totalCallCount},</if>
            <if test="lastAccessTime != null">last_access_time = #{lastAccessTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKeySecretTokenUsageById" parameterType="Long">
        delete from s_key_secret_token_usage where id = #{id}
    </delete>

    <delete id="deleteKeySecretTokenUsageByIds" parameterType="String">
        delete from s_key_secret_token_usage where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateUsageAtomic">
        UPDATE s_key_secret_token_usage
        SET point_usage = point_usage + #{pointCost},
            total_call_count = total_call_count + 1,
            last_access_time = NOW(),
            update_time = NOW()
        WHERE token = #{token}
          AND status = '0'
          AND (total_point_count - point_usage) >= #{pointCost}
    </update>
</mapper>

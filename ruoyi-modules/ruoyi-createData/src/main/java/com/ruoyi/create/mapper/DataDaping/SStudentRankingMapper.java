package com.ruoyi.create.mapper.DataDaping;

import com.ruoyi.create.domain.DataDaping.SStudentRanking;

import java.util.List;


/**
 * 学生活跃度排行Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-08
 */
public interface SStudentRankingMapper 
{
    List<SStudentRanking> selectLatestStudentRankingList();

    /**
     * 查询学生活跃度排行
     * 
     * @param id 学生活跃度排行主键
     * @return 学生活跃度排行
     */
    public SStudentRanking selectSStudentRankingById(Long id);

    /**
     * 查询学生活跃度排行列表
     * 
     * @param sStudentRanking 学生活跃度排行
     * @return 学生活跃度排行集合
     */
    public List<SStudentRanking> selectSStudentRankingList(SStudentRanking sStudentRanking);

    /**
     * 新增学生活跃度排行
     * 
     * @param sStudentRanking 学生活跃度排行
     * @return 结果
     */
    public int insertSStudentRanking(SStudentRanking sStudentRanking);

    /**
     * 修改学生活跃度排行
     * 
     * @param sStudentRanking 学生活跃度排行
     * @return 结果
     */
    public int updateSStudentRanking(SStudentRanking sStudentRanking);

    /**
     * 删除学生活跃度排行
     * 
     * @param id 学生活跃度排行主键
     * @return 结果
     */
    public int deleteSStudentRankingById(Long id);

    /**
     * 批量删除学生活跃度排行
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSStudentRankingByIds(Long[] ids);
}

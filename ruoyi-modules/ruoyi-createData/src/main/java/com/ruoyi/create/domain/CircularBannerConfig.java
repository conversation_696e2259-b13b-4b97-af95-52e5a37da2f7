package com.ruoyi.create.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 轮播图配置 s_circular_banner_config
 *
 * <AUTHOR>
 * @date 2024-09-04
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CircularBannerConfig extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 文件名称 */
    @TableField("file_name")
    @Excel(name = "文件名称")
    private String fileName;

    /** 上传文件ID */
    @TableField("file_id")
    @Excel(name = "上传文件ID")
    private String fileId;

    /** 轮播图名称 */
    @Excel(name = "轮播图名称")
    private String title;

    /** 轮播图路径 */
    @TableField("image_url")
    @Excel(name = "轮播图路径")
    private String imageUrl;

    /** 轮播图描述 */
    @Excel(name = "轮播图描述")
    private String description;

    /** 学校名称 */
    @Excel(name = "学校名称")
    private String school;

    /**
     * 创建人
     **/
    @TableField("create_by")
    @Excel(name = "创建人")
    private String createBy;

    /**
     * 创建时间
     **/
    @TableField("create_time")
    @Excel(name = "创建时间")
    private Date createTime;

    /**
     * 更新人
     **/
    @TableField("update_by")
    @Excel(name = "更新人")
    private String updateBy;

    /**
     * 更新时间
     **/
    @TableField("update_time")
    @Excel(name = "更新时间")
    private Date updateTime;

    private List<Map<String, String>> presentationFileList;

    public List<Map<String, String>> getPresentationFileList() {
        return presentationFileList;
    }

    public void setPresentationFileList(List<Map<String, String>> presentationFileList) {
        this.presentationFileList = presentationFileList;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String cirId) {
        this.fileName = cirId;
    }

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getSchool() {
        return school;
    }

    public void setSchool(String school) {
        this.school = school;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "CircularBannerConfig{" +
                "id=" + id +
                ", fileName='" + fileName + '\'' +
                ", fileId='" + fileId + '\'' +
                ", title='" + title + '\'' +
                ", imageUrl='" + imageUrl + '\'' +
                ", description='" + description + '\'' +
                ", school='" + school + '\'' +
                ", createBy='" + createBy + '\'' +
                ", createTime=" + createTime +
                ", updateBy='" + updateBy + '\'' +
                ", updateTime=" + updateTime +
                '}';
    }
}

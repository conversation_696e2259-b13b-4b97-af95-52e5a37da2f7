package com.ruoyi.create.service;

import com.ruoyi.create.domain.CodeExecution;
import com.ruoyi.create.domain.ExecutionResult;

import java.util.List;

/**
 * Docker代码执行服务接口
 *
 * <AUTHOR>
 * @date 2024-07-30
 */
public interface IDockerExecutionService {

    /**
     * 执行代码
     *
     * @param request 执行请求
     * @return 执行结果
     */
    ExecutionResult executeCode(CodeExecution request);

    /**
     * 异步执行代码
     *
     * @param request 执行请求
     * @return 执行ID
     */
    String executeCodeAsync(CodeExecution request);

    /**
     * 获取执行结果
     *
     * @param executionId 执行ID
     * @return 执行结果
     */
    ExecutionResult getExecutionResult(String executionId);

    /**
     * 停止代码执行
     *
     * @param executionId 执行ID
     * @return 是否成功
     */
    boolean stopExecution(String executionId);

    /**
     * 获取所有正在运行的执行任务
     *
     * @return 执行结果列表
     */
    List<ExecutionResult> getRunningExecutions();

    /**
     * 清理过期容器
     *
     * @return 清理的容器数量
     */
    int cleanupExpiredContainers();

    /**
     * 获取可用的Python包列表
     *
     * @return Python包列表
     */
    List<String> getAvailablePythonPackages();

    /**
     * 检查Docker服务状态
     *
     * @return 是否可用
     */
    boolean isDockerAvailable();

    /**
     * 获取用户的专属容器ID
     *
     * @param userId 用户ID
     * @return 容器ID，如果不存在则返回null
     */
    String getUserContainerId(Long userId);

    /**
     * 停止并删除用户的专属容器
     *
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean removeUserContainer(Long userId);

    /**
     * 获取所有用户容器的状态信息
     *
     * @return 用户容器状态映射
     */
    java.util.Map<Long, String> getAllUserContainers();

    /**
     * 清理Redis中的无效容器数据
     *
     * @return 清理的记录数量
     */
    int cleanupInvalidRedisData();

    /**
     * 获取容器日志
     *
     * @param containerId 容器ID
     * @param lines 日志行数，默认100行
     * @return 容器日志内容
     */
    String getContainerLogs(String containerId, Integer lines);
}

package com.ruoyi.create.mapper.DataDaping;

import com.ruoyi.create.domain.DataDaping.SDailyTrend;

import java.util.List;


/**
 * 每日趋势Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-08
 */
public interface SDailyTrendMapper 
{
    List<SDailyTrend> selectLatestDailyTrendList();

    /**
     * 查询每日趋势
     * 
     * @param id 每日趋势主键
     * @return 每日趋势
     */
    public SDailyTrend selectSDailyTrendById(Long id);

    /**
     * 查询每日趋势列表
     * 
     * @param sDailyTrend 每日趋势
     * @return 每日趋势集合
     */
    public List<SDailyTrend> selectSDailyTrendList(SDailyTrend sDailyTrend);

    /**
     * 新增每日趋势
     * 
     * @param sDailyTrend 每日趋势
     * @return 结果
     */
    public int insertSDailyTrend(SDailyTrend sDailyTrend);

    /**
     * 修改每日趋势
     * 
     * @param sDailyTrend 每日趋势
     * @return 结果
     */
    public int updateSDailyTrend(SDailyTrend sDailyTrend);

    /**
     * 删除每日趋势
     * 
     * @param id 每日趋势主键
     * @return 结果
     */
    public int deleteSDailyTrendById(Long id);

    /**
     * 批量删除每日趋势
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSDailyTrendByIds(Long[] ids);
}

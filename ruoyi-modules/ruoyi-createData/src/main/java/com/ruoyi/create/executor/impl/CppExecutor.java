package com.ruoyi.create.executor.impl;

import com.ruoyi.create.domain.CodeExecution;
import com.ruoyi.create.domain.ExecutionResult;
import com.ruoyi.create.executor.AbstractLanguageExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * C++代码执行器
 *
 * <AUTHOR>
 * @date 2024-07-31
 */
@Slf4j
@Component
public class CppExecutor extends AbstractLanguageExecutor {

    @Override
    public String getSupportedLanguage() {
        return "cpp";
    }

    @Override
    public String getDockerImage() {
        return "multi-lang-executor:latest";
    }

    @Override
    public String getFileExtension() {
        return ".cpp";
    }

    @Override
    public String getDefaultFileName() {
        return "main.cpp";
    }

    @Override
    public String[] getExecuteCommand(String codeFilePath, String input) {
        // C++需要先编译再执行
        String executablePath = codeFilePath.replace(".cpp", "");
        return new String[]{"sh", "-c",
            String.format("g++ -o %s %s && %s", executablePath, codeFilePath, executablePath)};
    }

    @Override
    protected ExecutionResult executeCodeInContainer(String containerId,
                                                   CodeExecution codeExecution,
                                                   String codeFilePath,
                                                   com.ruoyi.create.service.impl.DockerExecutionServiceImpl executionService) throws Exception {

        String executablePath = codeFilePath.replace(".cpp", "");

        // 构建编译和执行命令
        String compileAndRunCmd = buildCppExecuteCommand(codeFilePath, executablePath, codeExecution.getInput());

        log.info("执行C++编译和运行命令: {}", compileAndRunCmd);

        long startTime = System.currentTimeMillis();
        ExecutionResult executionResult = executionService.execCommandWithExitCode(containerId, new String[]{"sh", "-c", compileAndRunCmd});
        long executionTime = System.currentTimeMillis() - startTime;
        executionResult.setExecutionTime(executionTime);

        return executionResult;
    }

    /**
     * 构建C++编译和执行命令
     */
    private String buildCppExecuteCommand(String codeFilePath, String executablePath, String input) {
        StringBuilder cmdBuilder = new StringBuilder();

        // 添加开始日志
        cmdBuilder.append("echo \"[$(date \"+%Y-%m-%d %H:%M:%S\")] 开始编译C++代码 \" | tee /proc/1/fd/1; ");

        // 编译C++代码（使用C++17标准）
        cmdBuilder.append("g++ -std=c++17 -o ").append(executablePath).append(" ").append(codeFilePath)
                 .append(" 2>&1 | tee /proc/1/fd/1; ");

        // 检查编译是否成功
        cmdBuilder.append("if [ $? -eq 0 ]; then ");
        cmdBuilder.append("echo \"[$(date \"+%Y-%m-%d %H:%M:%S\")] C++编译成功，开始执行 \" | tee /proc/1/fd/1; ");

        // 如果有输入数据，记录输入
        if (input != null && !input.trim().isEmpty()) {
            cmdBuilder.append("echo \"[$(date \"+%Y-%m-%d %H:%M:%S\")] 输入数据已设置\" | tee /proc/1/fd/1; ");

            // 执行C++程序（带输入）
            cmdBuilder.append("cat << 'CPPINPUT' | ").append(executablePath).append(" 2>&1 | tee /proc/1/fd/1\n")
                     .append(input).append("\nCPPINPUT; ");
        } else {
            // 执行C++程序（无输入）
            cmdBuilder.append(executablePath).append(" 2>&1 | tee /proc/1/fd/1; ");
        }

        cmdBuilder.append("else ");
        cmdBuilder.append("echo \"[$(date \"+%Y-%m-%d %H:%M:%S\")] C++编译失败 \" | tee /proc/1/fd/1; ");
        cmdBuilder.append("exit 1; ");
        cmdBuilder.append("fi");

        return cmdBuilder.toString();
    }

    @Override
    public String validateSyntax(String code) {
        if (code == null || code.trim().isEmpty()) {
            return "代码内容不能为空";
        }

        // 检查是否包含main函数
        if (!code.contains("int main") && !code.contains("void main")) {
            return "C++代码必须包含main函数";
        }

        // 检查基本的括号匹配
        int braceCount = 0;
        int parenCount = 0;

        for (char c : code.toCharArray()) {
            if (c == '{') braceCount++;
            if (c == '}') braceCount--;
            if (c == '(') parenCount++;
            if (c == ')') parenCount--;

            if (braceCount < 0) {
                return "大括号不匹配：多余的右大括号";
            }
            if (parenCount < 0) {
                return "小括号不匹配：多余的右小括号";
            }
        }

        if (braceCount != 0) {
            return "大括号不匹配：缺少" + (braceCount > 0 ? "右" : "左") + "大括号";
        }

        if (parenCount != 0) {
            return "小括号不匹配：缺少" + (parenCount > 0 ? "右" : "左") + "小括号";
        }

        // 检查是否包含iostream（常见的C++头文件）
        if (code.contains("cout") && !code.contains("#include <iostream>")) {
            return "使用cout需要包含#include <iostream>";
        }

        return null; // 验证通过
    }

    @Override
    public void installPackages(String containerId,
                               List<String> packages,
                               com.ruoyi.create.service.impl.DockerExecutionServiceImpl executionService) {
        // C++通常使用系统包管理器安装库
        if (packages == null || packages.isEmpty()) {
            return;
        }

        for (String packageName : packages) {
            try {
                log.info("正在安装C++库: {}", packageName);
                String installCmd = String.format(
                    "echo \"[$(date '+%%Y-%%m-%%d %%H:%%M:%%S')] 开始安装C++库: %s\" | tee /proc/1/fd/1 && " +
                    "apt-get update && apt-get install -y %s && " +
                    "echo \"[$(date '+%%Y-%%m-%%d %%H:%%M:%%S')] C++库安装成功: %s\" | tee /proc/1/fd/1",
                    packageName, packageName, packageName
                );
                executionService.execCommandInContainer(containerId, new String[]{"sh", "-c", installCmd});
                log.info("C++库安装成功: {}", packageName);
            } catch (Exception e) {
                log.warn("C++库安装失败: {}, 错误: {}", packageName, e.getMessage());
                // 继续安装其他包，不中断流程
            }
        }
    }
}

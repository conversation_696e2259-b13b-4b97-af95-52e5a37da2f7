package com.ruoyi.create.domain;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.util.List;

/**
 * 文献整理-关键词统计对象 s_literatrue_keyword_count
 * 
 * <AUTHOR>
 * @date 2024-07-18
 */
@Data
public class LiteratrueKeywordCount extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 文献标题ID */
    @Excel(name = "文献标题ID")
    private Long titleId;

    /** 文献标题 */
    @Excel(name = "文献标题")
    private String title;

    /**  主题 0-相关主题 1-作者 2-机构 */
    @Excel(name = " 主题 0-相关主题 1-作者 2-机构")
    private String subjectId;

    /** 类别 */
    @Excel(name = "类别")
    private String category;

    /** 关键词 */
    @Excel(name = "关键词")
    private String keyword;

    /** 关键词数量 */
    @Excel(name = "关键词数量")
    private Long count;

    /** 查询时区分相关主题、作者、机构  0-主题 1-作者 2-机构 */
    private String categoryFlag;

    /** 返回前端数据关联关系 */
    List<LiteratrueLinks> literatrueLinks;

    List<LiteratrueNodesData> literatrueNodesData;
}

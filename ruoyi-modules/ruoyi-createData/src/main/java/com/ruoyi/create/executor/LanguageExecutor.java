package com.ruoyi.create.executor;

import com.ruoyi.create.domain.CodeExecution;
import com.ruoyi.create.domain.ExecutionResult;

/**
 * 编程语言执行器接口
 * 定义了不同编程语言的代码执行规范
 *
 * <AUTHOR>
 * @date 2024-07-31
 */
public interface LanguageExecutor {

    /**
     * 获取支持的编程语言
     *
     * @return 编程语言名称（小写）
     */
    String getSupportedLanguage();

    /**
     * 获取Docker镜像名称
     *
     * @return Docker镜像名称
     */
    String getDockerImage();

    /**
     * 获取代码文件扩展名
     *
     * @return 文件扩展名（如：.py, .java, .c, .cpp, .js）
     */
    String getFileExtension();

    /**
     * 获取默认的代码文件名
     *
     * @return 默认文件名
     */
    String getDefaultFileName();

    /**
     * 执行代码
     *
     * @param codeExecution 代码执行请求
     * @param result 执行结果对象
     * @param containerId 容器ID
     * @param executionService Docker执行服务实例
     * @return 执行结果
     */
    ExecutionResult executeCode(CodeExecution codeExecution, 
                               ExecutionResult result, 
                               String containerId,
                               com.ruoyi.create.service.impl.DockerExecutionServiceImpl executionService);

    /**
     * 安装依赖包（如果支持）
     *
     * @param containerId 容器ID
     * @param packages 依赖包列表
     * @param executionService Docker执行服务实例
     */
    default void installPackages(String containerId, 
                               java.util.List<String> packages,
                               com.ruoyi.create.service.impl.DockerExecutionServiceImpl executionService) {
        // 默认实现：不支持包安装
    }

    /**
     * 验证代码语法（可选实现）
     *
     * @param code 代码内容
     * @return 验证结果，null表示验证通过
     */
    default String validateSyntax(String code) {
        // 默认实现：不进行语法验证
        return null;
    }

    /**
     * 获取执行命令
     *
     * @param codeFilePath 代码文件路径
     * @param input 输入数据
     * @return 执行命令数组
     */
    String[] getExecuteCommand(String codeFilePath, String input);

    /**
     * 处理代码文件
     *
     * @param containerId 容器ID
     * @param codeExecution 代码执行请求
     * @param workspacePath 工作目录路径
     * @param executionService Docker执行服务实例
     * @return 代码文件路径
     * @throws Exception 处理异常
     */
    String handleCodeFile(String containerId, 
                         CodeExecution codeExecution, 
                         String workspacePath,
                         com.ruoyi.create.service.impl.DockerExecutionServiceImpl executionService) throws Exception;
}

package com.ruoyi.create.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.AssertTrue;
import java.util.List;

/**
 * 代码执行请求实体 - HTTP请求参数，不需要类型信息
 *
 * <AUTHOR>
 * @date 2024-07-30
 */
@Data
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CodeExecution {

    /** 代码内容（直接输入的代码） */
    private String code;

    /** 代码内容 是否进行base64编码 */
    private Boolean base64 = false;

    /** 上传的代码文件路径（服务器本地路径） */
    private String codeFilePath;

    /** 上传的代码文件名 */
    private String codeFileName;

    /** 编程语言，支持：python, java, c, cpp, javascript */
    @NotBlank(message = "编程语言不能为空")
    private String language;

    /** 输入数据（可选） */
    private String input;

    /** 执行超时时间（秒），默认30秒 */
    private Integer timeoutSeconds = 30;

    /** 内存限制（MB），默认128MB */
    private Integer memoryLimitMB = 128;

    /** CPU限制（核心数），默认1核 */
    private Double cpuLimit = 1.0;

    /** 需要安装的Python包列表（已弃用，使用packages替代） */
    @Deprecated
    private List<String> pythonPackages;

    /** 需要安装的依赖包列表（通用，适用于所有语言） */
    private List<String> packages;

    /** 是否需要网络访问，默认false */
    private Boolean networkAccess = false;

    /** 执行环境变量 */
    private String environmentVars;

    /** 用户ID（用于日志记录） */
    private String userId;

    /** 任务描述 */
    private String description;

    /**
     * 验证代码内容或文件路径至少有一个
     */
    @AssertTrue(message = "代码内容和文件路径至少需要提供一个")
    public boolean isCodeOrFileProvided() {
        return (code != null && !code.trim().isEmpty()) ||
               (codeFilePath != null && !codeFilePath.trim().isEmpty());
    }
}

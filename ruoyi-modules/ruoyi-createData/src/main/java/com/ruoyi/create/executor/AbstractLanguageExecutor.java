package com.ruoyi.create.executor;

import com.ruoyi.create.domain.CodeExecution;
import com.ruoyi.create.domain.ExecutionResult;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 抽象语言执行器基类
 * 提供通用的代码执行逻辑
 *
 * <AUTHOR>
 * @date 2024-07-31
 */
@Slf4j
public abstract class AbstractLanguageExecutor implements LanguageExecutor {

    @Override
    public ExecutionResult executeCode(CodeExecution codeExecution,
                                     ExecutionResult result,
                                     String containerId,
                                     com.ruoyi.create.service.impl.DockerExecutionServiceImpl executionService) {
        long startTime = System.currentTimeMillis();
        try {
            log.info("开始执行{}代码，容器ID: {}", getSupportedLanguage().toUpperCase(), containerId);

            // 确保工作目录存在
            String workspacePath = "/home/<USER>";
            ensureWorkspaceExists(containerId, workspacePath, executionService);

            // 设置容器日志环境
            setupContainerLogging(containerId, executionService);

            // 安装依赖包（如果需要）
            if (codeExecution.getPackages() != null && !codeExecution.getPackages().isEmpty()) {
                installPackages(containerId, codeExecution.getPackages(), executionService);
            }

            // 处理代码文件
            String codeFilePath = handleCodeFile(containerId, codeExecution, workspacePath, executionService);

            // 执行代码
            ExecutionResult executionResult = executeCodeInContainer(containerId, codeExecution, codeFilePath, executionService);

            // 更新结果
            if ("SUCCESS".equals(executionResult.getStatus())) {
                result = ExecutionResult.success(result.getExecutionId(), executionResult.getStdout(), executionResult.getExecutionTime())
                        .setContainerId(containerId)
                        .setStartTime(result.getStartTime())
                        .setCreateTime(result.getCreateTime());
            } else if ("ERROR".equals(executionResult.getStatus())) {
                result = ExecutionResult.error(result.getExecutionId(), executionResult.getStderr(), executionResult.getErrorMessage())
                        .setContainerId(containerId)
                        .setExecutionTime(executionResult.getExecutionTime())
                        .setStartTime(result.getStartTime())
                        .setCreateTime(result.getCreateTime())
                        .setExitCode(1);
            }

            log.info("{}代码执行完成，执行ID: {}, 耗时: {}ms", getSupportedLanguage().toUpperCase(), result.getExecutionId(), executionResult.getExecutionTime());
            return result;

        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            log.error("{}代码执行失败: {}", getSupportedLanguage().toUpperCase(), e.getMessage(), e);

            String errorOutput = e.getMessage() != null ? e.getMessage() : "代码执行失败";
            return ExecutionResult.error(result.getExecutionId(), errorOutput, e.getMessage())
                    .setContainerId(containerId)
                    .setExecutionTime(executionTime)
                    .setStartTime(result.getStartTime())
                    .setCreateTime(result.getCreateTime())
                    .setExitCode(1);
        }
    }

    @Override
    public String handleCodeFile(String containerId,
                                CodeExecution codeExecution,
                                String workspacePath,
                                com.ruoyi.create.service.impl.DockerExecutionServiceImpl executionService) throws Exception {
        String codeFileName;
        String containerFilePath;

        if (codeExecution.getCode() != null && !codeExecution.getCode().trim().isEmpty()) {
            // 直接输入的代码
            codeFileName = getDefaultFileName();
            containerFilePath = workspacePath + "/" + codeFileName;

            // 创建代码文件并记录日志
            String createFileCmd = String.format(
                    "echo \"[$(date '+%%Y-%%m-%%d %%H:%%M:%%S')] 创建%s代码文件: %s\" | tee /proc/1/fd/1 && " +
                            "cat > %s << 'CODECONTENT'\n%s\nCODECONTENT\n" +
                            "echo \"[$(date '+%%Y-%%m-%%d %%H:%%M:%%S')] 代码文件创建完成，大小: $(wc -c < %s)字节\" | tee /proc/1/fd/1",
                    getSupportedLanguage().toUpperCase(),
                    containerFilePath,
                    containerFilePath,
                    codeExecution.getCode(),
                    containerFilePath
            );
            executionService.execCommandInContainer(containerId, new String[]{"sh", "-c", createFileCmd});
            log.debug("{}代码已写入文件: {}", getSupportedLanguage().toUpperCase(), containerFilePath);

        } else if (codeExecution.getCodeFilePath() != null && !codeExecution.getCodeFilePath().trim().isEmpty()) {
            // 文件上传的情况
            codeFileName = codeExecution.getCodeFileName() != null ? codeExecution.getCodeFileName() : getDefaultFileName();
            containerFilePath = workspacePath + "/" + codeFileName;

            // 记录文件上传日志
            String logCmd = String.format("echo \"[$(date '+%%Y-%%m-%%d %%H:%%M:%%S')] 开始上传%s文件: %s\" | tee /proc/1/fd/1",
                    getSupportedLanguage().toUpperCase(), codeFileName);
            executionService.execCommandInContainer(containerId, new String[]{"sh", "-c", logCmd});

            // 将本地文件复制到容器中
            copyFileToContainer(containerId, codeExecution.getCodeFilePath(), containerFilePath, executionService);

            // 记录上传完成日志
            String completeLogCmd = String.format("echo \"[$(date '+%%Y-%%m-%%d %%H:%%M:%%S')] %s文件上传完成: %s，大小: $(wc -c < %s)字节\" | tee /proc/1/fd/1",
                    getSupportedLanguage().toUpperCase(), codeFileName, containerFilePath);
            executionService.execCommandInContainer(containerId, new String[]{"sh", "-c", completeLogCmd});

            log.debug("{}文件已复制到容器: {} -> {}", getSupportedLanguage().toUpperCase(), codeExecution.getCodeFilePath(), containerFilePath);

        } else {
            throw new RuntimeException("代码内容和文件路径都为空，请提供代码内容或上传文件");
        }

        return containerFilePath;
    }

    /**
     * 在容器中执行代码
     */
    protected ExecutionResult executeCodeInContainer(String containerId,
                                                   CodeExecution codeExecution,
                                                   String codeFilePath,
                                                   com.ruoyi.create.service.impl.DockerExecutionServiceImpl executionService) throws Exception {
        // 构建执行命令
        String[] executeCommand = getExecuteCommand(codeFilePath, codeExecution.getInput());

        // 构建完整的Shell命令，包含日志记录
        String fullCommand = buildExecuteCommand(executeCommand, codeExecution.getInput());

        log.info("执行{}命令: {}", getSupportedLanguage().toUpperCase(), fullCommand);

        long startTime = System.currentTimeMillis();
        ExecutionResult executionResult = executionService.execCommandWithExitCode(containerId, new String[]{"sh", "-c", fullCommand});
        long executionTime = System.currentTimeMillis() - startTime;
        executionResult.setExecutionTime(executionTime);

        return executionResult;
    }

    /**
     * 构建执行命令字符串
     */
    protected String buildExecuteCommand(String[] executeCommand, String input) {
        StringBuilder cmdBuilder = new StringBuilder();

        // 添加开始日志
        cmdBuilder.append("echo \"[$(date \"+%Y-%m-%d %H:%M:%S\")] 开始执行")
                 .append(getSupportedLanguage().toUpperCase())
                 .append("代码\" | tee /proc/1/fd/1; ");

        // 构建执行命令字符串
        StringBuilder execCmdBuilder = new StringBuilder();
        for (int i = 0; i < executeCommand.length; i++) {
            if (i > 0) execCmdBuilder.append(" ");
            execCmdBuilder.append(executeCommand[i]);
        }
        String execCmd = execCmdBuilder.toString();

        // 如果有输入数据，记录输入
        if (input != null && !input.trim().isEmpty()) {
            cmdBuilder.append("echo \"[$(date \"+%Y-%m-%d %H:%M:%S\")] 输入数据已设置\" | tee /proc/1/fd/1; ");

            // 构建带输入的执行命令
            cmdBuilder.append("cat << 'INPUTDATA' | ")
                     .append(execCmd)
                     .append(" 2>&1 | tee /proc/1/fd/1\n")
                     .append(input)
                     .append("\nINPUTDATA; ");
        } else {
            // 构建无输入的执行命令
            cmdBuilder.append(execCmd).append(" 2>&1 | tee /proc/1/fd/1; ");
        }

        return cmdBuilder.toString();
    }

    /**
     * 确保工作目录存在
     */
    protected void ensureWorkspaceExists(String containerId, String workspacePath,
                                       com.ruoyi.create.service.impl.DockerExecutionServiceImpl executionService) throws Exception {
        try {
            executionService.execCommandInContainer(containerId, new String[]{"test", "-d", workspacePath});
        } catch (Exception e) {
            // 目录不存在，创建它
            executionService.execCommandInContainer(containerId, new String[]{"mkdir", "-p", workspacePath});
            executionService.execCommandInContainer(containerId, new String[]{"chmod", "755", workspacePath});
            log.debug("创建容器工作目录: {}", workspacePath);
        }
    }

    /**
     * 设置容器日志环境
     */
    protected void setupContainerLogging(String containerId,
                                       com.ruoyi.create.service.impl.DockerExecutionServiceImpl executionService) {
        try {
            String logScript = String.format(
                    "echo \"[$(date '+%%Y-%%m-%%d %%H:%%M:%%S')] %s容器启动完成，ID: %s\" | tee /proc/1/fd/1; " +
                    "echo \"[$(date '+%%Y-%%m-%%d %%H:%%M:%%S')] 工作目录: $(pwd)\" | tee /proc/1/fd/1; " +
                    "echo \"[$(date '+%%Y-%%m-%%d %%H:%%M:%%S')] %s环境准备就绪，等待代码执行...\" | tee /proc/1/fd/1",
                    getSupportedLanguage().toUpperCase(), containerId, getSupportedLanguage().toUpperCase()
            );

            executionService.execCommandInContainer(containerId, new String[]{"sh", "-c", logScript});
            log.debug("{}容器日志环境设置完成: {}", getSupportedLanguage().toUpperCase(), containerId);
        } catch (Exception e) {
            log.warn("设置{}容器日志环境失败: {}, 错误: {}", getSupportedLanguage().toUpperCase(), containerId, e.getMessage());
        }
    }

    /**
     * 将本地文件复制到容器中
     */
    protected void copyFileToContainer(String containerId, String localFilePath, String containerFilePath,
                                     com.ruoyi.create.service.impl.DockerExecutionServiceImpl executionService) throws Exception {
        // 读取本地文件内容
        Path path = Paths.get(localFilePath);
        if (!Files.exists(path)) {
            throw new RuntimeException("本地文件不存在: " + localFilePath);
        }

        byte[] bytes = Files.readAllBytes(path);
        String fileContent = new String(bytes, StandardCharsets.UTF_8);

        // 将文件内容写入容器
        String createFileCmd = String.format("cat > %s << 'EOF'\n%s\nEOF", containerFilePath, fileContent);
        executionService.execCommandInContainer(containerId, new String[]{"sh", "-c", createFileCmd});

        log.debug("{}文件复制完成: {} -> {}", getSupportedLanguage().toUpperCase(), localFilePath, containerFilePath);
    }


}

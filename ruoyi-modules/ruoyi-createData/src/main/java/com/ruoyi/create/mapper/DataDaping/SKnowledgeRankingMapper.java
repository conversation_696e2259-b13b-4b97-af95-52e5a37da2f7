package com.ruoyi.create.mapper.DataDaping;

import com.ruoyi.create.domain.DataDaping.SKnowledgeRanking;

import java.util.List;


/**
 * 专业知识点热度排行Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-08
 */
public interface SKnowledgeRankingMapper 
{
    List<SKnowledgeRanking> selectLatestKnowledgeRankingList();

    /**
     * 查询专业知识点热度排行
     * 
     * @param id 专业知识点热度排行主键
     * @return 专业知识点热度排行
     */
    public SKnowledgeRanking selectSKnowledgeRankingById(Long id);

    /**
     * 查询专业知识点热度排行列表
     * 
     * @param sKnowledgeRanking 专业知识点热度排行
     * @return 专业知识点热度排行集合
     */
    public List<SKnowledgeRanking> selectSKnowledgeRankingList(SKnowledgeRanking sKnowledgeRanking);

    /**
     * 新增专业知识点热度排行
     * 
     * @param sKnowledgeRanking 专业知识点热度排行
     * @return 结果
     */
    public int insertSKnowledgeRanking(SKnowledgeRanking sKnowledgeRanking);

    /**
     * 修改专业知识点热度排行
     * 
     * @param sKnowledgeRanking 专业知识点热度排行
     * @return 结果
     */
    public int updateSKnowledgeRanking(SKnowledgeRanking sKnowledgeRanking);

    /**
     * 删除专业知识点热度排行
     * 
     * @param id 专业知识点热度排行主键
     * @return 结果
     */
    public int deleteSKnowledgeRankingById(Long id);

    /**
     * 批量删除专业知识点热度排行
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSKnowledgeRankingByIds(Long[] ids);
}

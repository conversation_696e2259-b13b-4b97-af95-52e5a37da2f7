package com.ruoyi.create.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;
import org.redisson.codec.JsonJacksonCodec;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Redis序列化配置 - 只配置序列化器
 *
 * <AUTHOR>
 * @date 2024-08-05
 */
@Slf4j
@Configuration
public class RedissonConfig {

    /**
     * 配置Redis专用的ObjectMapper - 简洁JSON格式，无类型信息
     */
    @Bean("redisObjectMapper")
    public ObjectMapper redisObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();

        // 注册Java 8时间模块，支持LocalDateTime序列化
        objectMapper.registerModule(new JavaTimeModule());

        log.info("Redis ObjectMapper配置完成 - 简洁JSON格式，无类型信息");
        return objectMapper;
    }

    /**
     * 配置JSON编解码器 - 供业务代码使用
     */
    @Bean("redisJsonCodec")
    public JsonJacksonCodec redisJsonCodec() {
        JsonJacksonCodec codec = new JsonJacksonCodec(redisObjectMapper());
        log.info("Redis JSON编解码器配置完成");
        return codec;
    }
}

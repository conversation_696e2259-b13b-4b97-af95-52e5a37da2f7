package com.ruoyi.create.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.codec.JsonJacksonCodec;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * Redisson配置类 - 最小化修改解决Redis缓存乱码问题
 * 全局配置JSON序列化器，业务代码无需修改
 * 
 * <AUTHOR>
 * @date 2024-08-05
 */
@Slf4j
@Configuration
public class RedissonConfig {

    @Value("${spring.redis.host:localhost}")
    private String redisHost;

    @Value("${spring.redis.port:6379}")
    private int redisPort;

    @Value("${spring.redis.password:}")
    private String redisPassword;

    @Value("${spring.redis.database:0}")
    private int database;

    @Value("${spring.redis.timeout:3000}")
    private int timeout;

    // 主从模式配置
    @Value("${spring.redis.sentinel.master:}")
    private String sentinelMaster;

    @Value("${spring.redis.sentinel.nodes:}")
    private String sentinelNodes;

    // Redis模式：single(单机) 或 sentinel(主从)
    @Value("${spring.redis.mode:single}")
    private String redisMode;

    /**
     * 配置专用于Redis的ObjectMapper
     * 关键：不启用类型信息，避免 ["java.lang.Long",1] 格式
     */
    @Bean("redisObjectMapper")
    public ObjectMapper redisObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        
        // 注册Java 8时间模块，支持LocalDateTime序列化
        objectMapper.registerModule(new JavaTimeModule());
        
        // 关键配置：不启用类型信息，数据格式简洁
        // Long存储为：1 而不是 ["java.lang.Long",1]
        // List存储为：["item1","item2"] 而不是 ["java.util.ArrayList",["item1","item2"]]
        
        log.info("Redis ObjectMapper配置完成 - 简洁JSON格式，无类型信息");
        return objectMapper;
    }

    /**
     * 单机模式RedissonClient配置
     */
    @Bean
    @Primary
    @ConditionalOnProperty(name = "spring.redis.mode", havingValue = "single", matchIfMissing = true)
    public RedissonClient singleRedissonClient() {
        Config config = new Config();
        
        // 配置单机模式
        String redisUrl = String.format("redis://%s:%d", redisHost, redisPort);
        config.useSingleServer()
                .setAddress(redisUrl)
                .setDatabase(database)
                .setTimeout(timeout)
                .setConnectionMinimumIdleSize(1)
                .setConnectionPoolSize(10)
                .setIdleConnectionTimeout(10000)
                .setConnectTimeout(10000)
                .setRetryAttempts(3)
                .setRetryInterval(1500);
        
        // 设置密码（如果有）
        if (redisPassword != null && !redisPassword.trim().isEmpty()) {
            config.useSingleServer().setPassword(redisPassword);
        }
        
        // 设置全局JSON编解码器 - 关键配置，业务代码无需修改
        config.setCodec(new JsonJacksonCodec(redisObjectMapper()));
        
        log.info("Redis单机模式配置完成: {}:{}, 数据库: {}, 使用简洁JSON格式", 
                redisHost, redisPort, database);
        return Redisson.create(config);
    }

    /**
     * 主从模式RedissonClient配置
     */
    @Bean
    @Primary
    @ConditionalOnProperty(name = "spring.redis.mode", havingValue = "sentinel")
    public RedissonClient sentinelRedissonClient() {
        Config config = new Config();
        
        // 配置哨兵模式
        String[] nodes = sentinelNodes.split(",");
        config.useSentinelServers()
                .setMasterName(sentinelMaster)
                .setDatabase(database)
                .setTimeout(timeout)
                .setMasterConnectionMinimumIdleSize(1)
                .setMasterConnectionPoolSize(10)
                .setSlaveConnectionMinimumIdleSize(1)
                .setSlaveConnectionPoolSize(10)
                .setIdleConnectionTimeout(10000)
                .setConnectTimeout(10000)
                .setRetryAttempts(3)
                .setRetryInterval(1500);
        
        // 添加哨兵节点
        for (String node : nodes) {
            String trimmedNode = node.trim();
            if (!trimmedNode.isEmpty()) {
                if (!trimmedNode.startsWith("redis://")) {
                    trimmedNode = "redis://" + trimmedNode;
                }
                config.useSentinelServers().addSentinelAddress(trimmedNode);
            }
        }
        
        // 设置密码（如果有）
        if (redisPassword != null && !redisPassword.trim().isEmpty()) {
            config.useSentinelServers().setPassword(redisPassword);
        }
        
        // 设置全局JSON编解码器 - 关键配置，业务代码无需修改
        config.setCodec(new JsonJacksonCodec(redisObjectMapper()));
        
        log.info("Redis哨兵模式配置完成: master={}, nodes={}, 数据库: {}, 使用简洁JSON格式", 
                sentinelMaster, sentinelNodes, database);
        return Redisson.create(config);
    }
}

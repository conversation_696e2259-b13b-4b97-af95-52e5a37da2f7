package com.ruoyi.create.mapper.DataDaping;

import com.ruoyi.create.domain.DataDaping.SPlatformReal;

import java.util.List;


/**
 * 平台实时数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-08
 */
public interface SPlatformRealMapper 
{
    SPlatformReal selectLatestPlatformReal();

    /**
     * 查询平台实时数据
     * 
     * @param id 平台实时数据主键
     * @return 平台实时数据
     */
    public SPlatformReal selectSPlatformRealById(Long id);

    /**
     * 查询平台实时数据列表
     * 
     * @param sPlatformReal 平台实时数据
     * @return 平台实时数据集合
     */
    public List<SPlatformReal> selectSPlatformRealList(SPlatformReal sPlatformReal);

    /**
     * 新增平台实时数据
     * 
     * @param sPlatformReal 平台实时数据
     * @return 结果
     */
    public int insertSPlatformReal(SPlatformReal sPlatformReal);

    /**
     * 修改平台实时数据
     * 
     * @param sPlatformReal 平台实时数据
     * @return 结果
     */
    public int updateSPlatformReal(SPlatformReal sPlatformReal);

    /**
     * 删除平台实时数据
     * 
     * @param id 平台实时数据主键
     * @return 结果
     */
    public int deleteSPlatformRealById(Long id);

    /**
     * 批量删除平台实时数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSPlatformRealByIds(Long[] ids);
}

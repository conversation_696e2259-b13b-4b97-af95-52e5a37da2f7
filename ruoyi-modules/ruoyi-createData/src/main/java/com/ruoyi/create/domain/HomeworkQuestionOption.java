package com.ruoyi.create.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.common.core.annotation.Excel;

/**
 * 作业题目选项表(HomeworkQuestionOption)表实体类
 *
 * <AUTHOR>
 * @since 2024-06-07 10:08:20
 */
@Data
@TableName("s_homework_question_option")
public class HomeworkQuestionOption {
    private static final long serialVersionUID = 1L;

    /**
     * 题目选项id
     **/
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 题目id
     **/
    @TableField("question_id")
    @Excel(name = "题目id")
    private Long questionId;

    /**
     * 选项标识
     **/
    @TableField("option_mark")
    @Excel(name = "选项标识")
    private String optionMark;

    /**
     * 选项文本
     **/
    @TableField("option_text")
    @Excel(name = "选项文本")
    private String optionText;


}


package com.ruoyi.create.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

/**
 * Web MVC配置 - 确保HTTP请求使用标准的ObjectMapper
 * 与Redis专用的ObjectMapper分离，避免冲突
 * 
 * <AUTHOR>
 * @date 2024-08-05
 */
@Slf4j
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    /**
     * 配置HTTP请求专用的ObjectMapper
     * 不启用类型信息，避免@class属性要求
     */
    @Bean
    @Primary
    public ObjectMapper httpObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        
        // 注册Java 8时间模块
        objectMapper.registerModule(new JavaTimeModule());
        
        // 不启用类型信息，避免HTTP请求需要@class属性
        // 这样HTTP请求的JSON反序列化就不会要求@class属性
        
        log.info("HTTP专用ObjectMapper配置完成 - 不启用类型信息，避免@class要求");
        return objectMapper;
    }

    /**
     * 配置HTTP消息转换器，使用标准的ObjectMapper
     */
    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter();
        converter.setObjectMapper(httpObjectMapper());
        converters.add(0, converter); // 添加到第一位，优先使用
        
        log.info("HTTP消息转换器配置完成 - 使用标准ObjectMapper");
    }
}

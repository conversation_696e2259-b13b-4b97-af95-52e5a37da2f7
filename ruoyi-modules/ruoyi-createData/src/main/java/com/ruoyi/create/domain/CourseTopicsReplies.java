package com.ruoyi.create.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

import java.util.List;

/**
 * 课程讨论话题回复对象 s_course_topics_replies
 * 
 * <AUTHOR>
 * @date 2024-12-04
 */
public class CourseTopicsReplies extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 课程讨论话题id */
    @Excel(name = "课程讨论话题id")
    private Long topicId;

    /** 留言id */
    private Long msgId;

    /** 留言内容 */
    @Excel(name = "留言内容")
    private String message;

    /** 留言用户id */
    @Excel(name = "留言用户id")
    private Long msgUserId;

    /** 用户昵称 */
    private String nickName;

    /** 留言与回复关联实体 */
    private List<TopicsReply> topicsReplyList;

    private  Boolean deleteFlag;

    /**
     * 总条数
     */
    private int count;

    /**
     * 点赞数量
     */
    private int coutKike;
    /**
     * 点踩数量
     */
    private int coutStep;
    /**
     * 当前用户赞踩  1赞/2踩
     */
    private String  kikeStep;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setTopicId(Long topicId) 
    {
        this.topicId = topicId;
    }

    public Long getTopicId() 
    {
        return topicId;
    }
    public void setMsgId(Long msgId) 
    {
        this.msgId = msgId;
    }

    public Long getMsgId() 
    {
        return msgId;
    }
    public void setMessage(String message) 
    {
        this.message = message;
    }

    public String getMessage() 
    {
        return message;
    }
    public void setMsgUserId(Long msgUserId) 
    {
        this.msgUserId = msgUserId;
    }

    public Long getMsgUserId() 
    {
        return msgUserId;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public List<TopicsReply> getTopicsReplyList() {
        return topicsReplyList;
    }

    public void setTopicsReplyList(List<TopicsReply> topicsReplyList) {
        this.topicsReplyList = topicsReplyList;
    }

    public Boolean getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(Boolean deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public int getCoutKike() {
        return coutKike;
    }

    public void setCoutKike(int coutKike) {
        this.coutKike = coutKike;
    }

    public int getCoutStep() {
        return coutStep;
    }

    public void setCoutStep(int coutStep) {
        this.coutStep = coutStep;
    }

    public String getKikeStep() {
        return kikeStep;
    }

    public void setKikeStep(String kikeStep) {
        this.kikeStep = kikeStep;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("topicId", getTopicId())
            .append("msgId", getMsgId())
            .append("message", getMessage())
            .append("msgUserId", getMsgUserId())
            .append("createTime", getCreateTime())
            .toString();
    }
}

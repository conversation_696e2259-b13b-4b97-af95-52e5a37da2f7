package com.ruoyi.create.domain;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 代码执行结果实体
 *
 * <AUTHOR>
 * @date 2024-07-30
 */
@Data
@Accessors(chain = true)
public class ExecutionResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 执行ID */
    private String executionId;

    /** 容器ID */
    private String containerId;

    /** 执行状态：RUNNING, SUCCESS, ERROR, TIMEOUT */
    private String status;

    /** 标准输出 */
    private String stdout;

    /** 错误输出 */
    private String stderr;

    /** 退出码 */
    private Integer exitCode;

    /** 执行时间（毫秒） */
    private Long executionTime;

    /** 内存使用（MB） */
    private Long memoryUsage;

    /** CPU使用率（%） */
    private Double cpuUsage;

    /** 开始时间 */
    private LocalDateTime startTime;

    /** 结束时间 */
    private LocalDateTime endTime;

    /** 错误信息 */
    private String errorMessage;

    /** 是否超时 */
    private Boolean timeout;

    /** 创建时间 */
    private LocalDateTime createTime;

    /** 容器销毁时间 */
    private LocalDateTime destroyTime;

    /**
     * 创建成功结果
     */
    public static ExecutionResult success(String executionId, String stdout, Long executionTime) {
        return new ExecutionResult()
                .setExecutionId(executionId)
                .setStatus("SUCCESS")
                .setStdout(stdout)
                .setExecutionTime(executionTime)
                .setExitCode(0)
                .setTimeout(false)
                .setEndTime(LocalDateTime.now());
    }

    /**
     * 创建错误结果
     */
    public static ExecutionResult error(String executionId, String stderr, String errorMessage) {
        return new ExecutionResult()
                .setExecutionId(executionId)
                .setStatus("ERROR")
                .setStderr(stderr)
                .setErrorMessage(errorMessage)
                .setExitCode(-1)
                .setTimeout(false)
                .setEndTime(LocalDateTime.now());
    }

    /**
     * 创建超时结果
     */
    public static ExecutionResult timeout(String executionId, Long executionTime) {
        return new ExecutionResult()
                .setExecutionId(executionId)
                .setStatus("TIMEOUT")
                .setExecutionTime(executionTime)
                .setTimeout(true)
                .setErrorMessage("代码执行超时")
                .setEndTime(LocalDateTime.now());
    }
}

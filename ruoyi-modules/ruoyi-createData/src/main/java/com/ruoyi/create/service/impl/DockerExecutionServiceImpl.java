package com.ruoyi.create.service.impl;

import com.github.dockerjava.api.DockerClient;
import com.github.dockerjava.api.async.ResultCallback;
import com.github.dockerjava.api.command.CreateContainerResponse;
import com.github.dockerjava.api.command.ExecCreateCmdResponse;
import com.github.dockerjava.api.model.Bind;
import com.github.dockerjava.api.model.HostConfig;
import com.github.dockerjava.api.model.PullResponseItem;
import com.github.dockerjava.api.model.Statistics;
import com.github.dockerjava.core.command.ExecStartResultCallback;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.config.DockerConfig;
import com.ruoyi.create.domain.CodeExecution;
import com.ruoyi.create.domain.ExecutionResult;
import com.ruoyi.create.executor.LanguageExecutor;
import com.ruoyi.create.executor.LanguageExecutorFactory;
import com.ruoyi.create.service.IDockerExecutionService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.Closeable;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Docker代码执行服务实现
 *
 * <AUTHOR>
 * @date 2024-07-30
 */
@Slf4j
@Service
public class DockerExecutionServiceImpl implements IDockerExecutionService {

    @Resource
    private DockerClient dockerClient;

    @Resource
    private DockerConfig dockerConfig;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private LanguageExecutorFactory executorFactory;

    // 分布式缓存 - 存储执行结果
    private RMap<String, ExecutionResult> executionResults;

    // 分布式缓存 - 存储用户容器映射关系 userId -> List<containerId>
    private RMap<Long, List<String>> userContainers;

    // 分布式缓存 - 存储容器创建时间 containerId -> createTime
    private RMap<String, LocalDateTime> containerCreateTimes;

    /**
     * 初始化分布式缓存 - 使用全局JSON序列化器，无需重复配置
     */
    @PostConstruct
    public void initRedissonMaps() {
        try {
            // 直接获取分布式Map，序列化器已在RedissonConfig中全局配置
            // 无需重复指定编解码器，避免代码重复
            executionResults = redissonClient.getMap("docker:execution:results");
            userContainers = redissonClient.getMap("docker:execution:containers");
            containerCreateTimes = redissonClient.getMap("docker:execution:times");

            // 设置缓存过期时间（防止数据无限累积）
            // 执行结果保留24小时
            executionResults.expireAsync(Duration.ofHours(24));
            // 用户容器映射保留1天（比容器生命周期长一些）
            userContainers.expireAsync(Duration.ofDays(1));
            // 容器创建时间保留1天
            containerCreateTimes.expireAsync(Duration.ofDays(1));

            log.info("Docker执行服务分布式缓存初始化完成");
            log.info("当前用户容器数量: {}", userContainers.size());
            log.info("当前执行结果数量: {}", executionResults.size());
            log.info("Redis缓存过期时间设置完成 - 使用全局JSON序列化器，数据可读性良好");

            // 测试缓存连接和序列化
            testCacheSerialization();

        } catch (Exception e) {
            log.error("Redis缓存初始化失败: {}", e.getMessage(), e);
            throw new RuntimeException("Redis缓存初始化失败", e);
        }
    }

    /**
     * 测试缓存序列化功能
     */
    private void testCacheSerialization() {
        try {
            // 测试ExecutionResult序列化
            ExecutionResult testResult = new ExecutionResult()
                    .setExecutionId("test-" + System.currentTimeMillis())
                    .setStatus("TEST")
                    .setStdout("测试中文输出 - Hello World!")
                    .setStartTime(LocalDateTime.now())
                    .setCreateTime(LocalDateTime.now());

            String testKey = "test:serialization:" + System.currentTimeMillis();
            executionResults.put(testKey, testResult);

            // 验证反序列化
            ExecutionResult retrieved = executionResults.get(testKey);
            if (retrieved != null && "TEST".equals(retrieved.getStatus())) {
                log.info("Redis JSON序列化测试成功 - 数据可正常读写，中文支持正常");
                // 清理测试数据
                executionResults.remove(testKey);
            } else {
                log.warn("Redis JSON序列化测试失败 - 数据读取异常");
            }

        } catch (Exception e) {
            log.warn("Redis序列化测试失败: {}", e.getMessage());
        }
    }

    @Override
    public ExecutionResult executeCode(CodeExecution codeExecution) {
        if (!isDockerAvailable()) {
            throw new RuntimeException("虚拟容器服务不可用，请联系管理员");
        }
        String executionId = generateExecutionId();
        log.info("开始执行代码，执行ID: {}, 语言: {}", executionId, codeExecution.getLanguage());

        if (codeExecution.getBase64()) {
            // 解码 Base64 编码的字符串
            byte[] decodedBytes = Base64.getDecoder().decode(codeExecution.getCode());
            // 将解码后的字节流转换为 UTF-8 字符串
            String decodedCode = new String(decodedBytes, StandardCharsets.UTF_8);
            // 设置解码后的代码
            codeExecution.setCode(decodedCode);
        }

        ExecutionResult result = new ExecutionResult()
                .setExecutionId(executionId)
                .setStatus("RUNNING")
                .setStartTime(LocalDateTime.now())
                .setCreateTime(LocalDateTime.now());

        executionResults.put(executionId, result);

        try {
            // 检查用户容器数量限制
            checkAndCleanupUserContainers(SecurityUtils.getUserId());

            // 根据语言选择执行方式
            return executeCodeByLanguage(codeExecution, result);

        } catch (Exception e) {
            log.error("代码执行失败，执行ID: {}, 错误: {}", executionId, e.getMessage(), e);
            result = ExecutionResult.error(executionId, "", e.getMessage());
            executionResults.put(executionId, result);
            return result;
        }
    }

    /**
     * 根据编程语言执行代码（新的统一入口）
     */
    private ExecutionResult executeCodeByLanguage(CodeExecution codeExecution, ExecutionResult result) {
        String language = codeExecution.getLanguage();

        // 验证语言是否支持
        if (!executorFactory.isLanguageSupported(language)) {
            throw new RuntimeException("不支持的编程语言: " + language +
                    "，支持的语言: " + executorFactory.getSupportedLanguages());
        }

        // 获取语言执行器
        LanguageExecutor executor = executorFactory.getExecutor(language);

        // 验证代码语法（可选）
        String syntaxError = executor.validateSyntax(codeExecution.getCode());
        if (syntaxError != null) {
            log.warn("代码语法验证失败: {}", syntaxError);
            return ExecutionResult.error(result.getExecutionId(), syntaxError, "代码语法错误")
                    .setStartTime(result.getStartTime())
                    .setCreateTime(result.getCreateTime())
                    .setExecutionTime(0L);
        }

        String containerId = null;
        long startTime = System.currentTimeMillis();

        try {
            // 获取当前用户ID
            Long userId = SecurityUtils.getUserId();
            log.info("用户 {} 请求执行{}代码", userId, language.toUpperCase());

            // 确保多语言镜像存在（统一使用多语言镜像）
            ensureMultiLanguageImageExists();

            // 获取或创建用户专属容器（统一使用多语言容器）
            containerId = getOrCreateMultiLanguageContainer(userId, codeExecution);
            result.setContainerId(containerId);

            log.info("用户 {} 使用{}容器: {}", userId, language.toUpperCase(), containerId);

            // 使用执行器执行代码
            ExecutionResult executionResult = executor.executeCode(codeExecution, result, containerId, this);

            log.info("{}代码执行完成，执行ID: {}, 耗时: {}ms",
                    language.toUpperCase(), result.getExecutionId(), executionResult.getExecutionTime());

            return executionResult;

        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            log.error("{}代码执行失败: {}", language.toUpperCase(), e.getMessage(), e);

            String errorOutput = e.getMessage() != null ? e.getMessage() : "代码执行失败";
            return ExecutionResult.error(result.getExecutionId(), errorOutput, e.getMessage())
                    .setContainerId(containerId)
                    .setExecutionTime(executionTime)
                    .setStartTime(result.getStartTime())
                    .setCreateTime(result.getCreateTime())
                    .setExitCode(1);
        }
    }

    /**
     * 确保指定语言的镜像存在
     */
    private void ensureLanguageImageExists(LanguageExecutor executor) {
        String image = executor.getDockerImage();
        try {
            dockerClient.inspectImageCmd(image).exec();
            log.info("{}镜像已存在: {}", executor.getSupportedLanguage().toUpperCase(), image);
        } catch (Exception e) {
            log.info("正在拉取{}镜像: {}", executor.getSupportedLanguage().toUpperCase(), image);
            try {
                dockerClient.pullImageCmd(image)
                        .exec(new ResultCallback.Adapter<PullResponseItem>() {
                            @Override
                            public void onNext(PullResponseItem item) {
                                if (item.getStatus() != null) {
                                    log.info("拉取进度: {}", item.getStatus());
                                }
                            }
                        })
                        .awaitCompletion();
                log.info("{}镜像拉取完成: {}", executor.getSupportedLanguage().toUpperCase(), image);
            } catch (InterruptedException ie) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("镜像拉取被中断", ie);
            }
        }
    }



    /**
     * 创建支持多语言的容器
     */
    private String createLanguageContainer(Long userId, CodeExecution codeExecution, LanguageExecutor executor) {
        String containerName = dockerConfig.getContainerPrefix() + "user-" + userId + "-" +
                executor.getSupportedLanguage() + "-" + System.currentTimeMillis();

        // 构建主机配置（资源限制）
        HostConfig hostConfig = HostConfig.newHostConfig()
                .withMemory(codeExecution.getMemoryLimitMB() * 1024L * 1024L) // 转换为字节
                .withCpuQuota((long) (codeExecution.getCpuLimit() * 100000)) // CPU配额
                .withCpuPeriod(100000L) // CPU周期
                .withNetworkMode(codeExecution.getNetworkAccess() ? "bridge" : "none") // 网络访问控制
                .withReadonlyRootfs(false) // 允许写入（代码执行需要）
                .withAutoRemove(false) // 不自动删除，我们手动管理
                .withBinds(Bind.parse("/etc/localtime:/etc/localtime:ro")); // 同步主机时区

        // 创建容器
        String image = executor.getDockerImage();
        String workspacePath = dockerConfig.getWorkspacePath();

        CreateContainerResponse container = dockerClient.createContainerCmd(image)
                .withName(containerName)
                .withHostConfig(hostConfig)
                .withCmd("sh", "-c", "while true; do sleep 30; done") // 保持容器运行
                .withWorkingDir(workspacePath)
                .withTty(true) // 启用TTY，便于交互和日志输出
                .withStdinOpen(true) // 保持stdin开放
                .withEnv("LANG=C.UTF-8", "LC_ALL=C.UTF-8", "LC_CTYPE=C.UTF-8", "PYTHONIOENCODING=utf-8", "TZ=Asia/Shanghai")
                .exec();

        return container.getId();
    }


    /**
     * 检查容器是否正在运行
     */
    private boolean isContainerRunning(String containerId) {
        try {
            return Boolean.TRUE.equals(dockerClient.inspectContainerCmd(containerId)
                    .exec()
                    .getState()
                    .getRunning());
        } catch (Exception e) {
            log.info("检查容器状态失败: {}, 错误: {}", containerId, e.getMessage());
            return false;
        }
    }


    @Override
    @Async
    public String executeCodeAsync(CodeExecution codeExecution) {
        String executionId = generateExecutionId();
        // 异步执行代码
        new Thread(() -> executeCode(codeExecution)).start();
        return executionId;
    }

    @Override
    public ExecutionResult getExecutionResult(String executionId) {
        return executionResults.get(executionId);
    }

    @Override
    public boolean stopExecution(String executionId) {
        ExecutionResult result = executionResults.get(executionId);
        if (result != null && result.getContainerId() != null) {
            try {
                dockerClient.stopContainerCmd(result.getContainerId()).exec();
                dockerClient.removeContainerCmd(result.getContainerId()).exec();
                result.setStatus("STOPPED").setEndTime(LocalDateTime.now());
                return true;
            } catch (Exception e) {
                log.error("停止容器失败: {}", e.getMessage(), e);
                return false;
            }
        }
        return false;
    }

    @Override
    public List<ExecutionResult> getRunningExecutions() {
        return executionResults.values().stream()
                .filter(result -> "RUNNING".equals(result.getStatus()))
                .collect(Collectors.toList());
    }

    @Override
    public int cleanupExpiredContainers() {
        int cleanedCount = 0;
        LocalDateTime expireTime = LocalDateTime.now().minusHours(dockerConfig.getContainerLifetimeHours());

        // 清理执行结果中的过期容器
        for (ExecutionResult result : executionResults.values()) {
            if (result.getCreateTime().isBefore(expireTime) && result.getContainerId() != null) {
                try {
                    dockerClient.removeContainerCmd(result.getContainerId()).withForce(true).exec();
                    result.setStatus("EXPIRED").setDestroyTime(LocalDateTime.now());
                    cleanedCount++;
                    log.info("清理过期执行容器: {}", result.getContainerId());
                } catch (Exception e) {
                    log.warn("清理执行容器失败: {}, 错误: {}", result.getContainerId(), e.getMessage());
                }
            }
        }

        // 清理用户专属容器
        cleanedCount += cleanupExpiredUserContainers(expireTime);

        return cleanedCount;
    }

    /**
     * 清理过期的用户专属容器
     */
    private int cleanupExpiredUserContainers(LocalDateTime expireTime) {
        int cleanedCount = 0;
        // 找出过期的容器
        List<String> expiredContainers = new ArrayList<>();
        List<Long> usersToRemove = new ArrayList<>();

        for (Map.Entry<String, LocalDateTime> entry : containerCreateTimes.entrySet()) {
            String containerId = entry.getKey();
            LocalDateTime createTime = entry.getValue();

            if (createTime.isBefore(expireTime)) {
                expiredContainers.add(containerId);

                // 找到对应的用户
                for (Map.Entry<Long, List<String>> userEntry : userContainers.entrySet()) {
                    if (userEntry.getValue() != null && userEntry.getValue().contains(containerId)) {
                        usersToRemove.add(userEntry.getKey());
                        break;
                    }
                }
            }
        }

        // 清理过期容器
        for (String containerId : expiredContainers) {
            try {
                dockerClient.removeContainerCmd(containerId).withForce(true).exec();
                containerCreateTimes.remove(containerId);
                cleanedCount++;
                log.info("清理过期用户容器: {}", containerId);
            } catch (Exception e) {
                log.warn("清理用户容器失败: {}, 错误: {}", containerId, e.getMessage());
            }
        }

        // 清理用户映射中的过期容器
        for (Long userId : usersToRemove) {
            List<String> userContainerList = userContainers.get(userId);
            if (userContainerList != null) {
                userContainerList.removeAll(expiredContainers);
                if (userContainerList.isEmpty()) {
                    userContainers.remove(userId);
                } else {
                    userContainers.put(userId, userContainerList);
                }
            }
            log.info("清理用户 {} 的过期容器映射", userId);
        }

        return cleanedCount;
    }

    /**
     * 检查并清理用户容器数量限制
     */
    private void checkAndCleanupUserContainers(Long userId) {
        Object userContainerObj = userContainers.get(userId);
        List<String> userContainerList;

        // 处理旧数据兼容性：如果是String类型，转换为List
        if (userContainerObj == null) {
            userContainerList = new ArrayList<>();
            userContainers.put(userId, userContainerList);
            return;
        } else if (userContainerObj instanceof String) {
            // 旧数据格式，转换为List
            userContainerList = new ArrayList<>();
            userContainerList.add((String) userContainerObj);
            userContainers.put(userId, userContainerList);
        } else if (userContainerObj instanceof List) {
            userContainerList = (List<String>) userContainerObj;
        } else {
            // 未知类型，重新初始化
            userContainerList = new ArrayList<>();
            userContainers.put(userId, userContainerList);
            return;
        }

        // 清理已停止的容器
        List<String> runningContainers = new ArrayList<>();
        for (String containerId : userContainerList) {
            if (isContainerRunning(containerId)) {
                runningContainers.add(containerId);
            } else {
                // 清理已停止的容器记录
                containerCreateTimes.remove(containerId);
                log.info("清理用户 {} 已停止的容器: {}", userId, containerId);
            }
        }

        // 如果运行中的容器数量超过限制，删除最旧的容器
        if (runningContainers.size() >= dockerConfig.getMaxUserContainers()) {
            int containersToRemove = runningContainers.size() - dockerConfig.getMaxUserContainers() + 1;

            // 按创建时间排序，删除最旧的容器
            runningContainers.sort((c1, c2) -> {
                LocalDateTime time1 = containerCreateTimes.get(c1);
                LocalDateTime time2 = containerCreateTimes.get(c2);
                if (time1 == null) return -1;
                if (time2 == null) return 1;
                return time1.compareTo(time2);
            });

            for (int i = 0; i < containersToRemove; i++) {
                String containerToRemove = runningContainers.get(i);
                try {
                    // 停止并删除容器
                    dockerClient.stopContainerCmd(containerToRemove).exec();
                    dockerClient.removeContainerCmd(containerToRemove).withForce(true).exec();

                    // 清理缓存
                    containerCreateTimes.remove(containerToRemove);
                    runningContainers.remove(containerToRemove);

                    log.info("用户 {} 超出容器限制，删除旧容器: {}", userId, containerToRemove);
                } catch (Exception e) {
                    log.warn("删除用户 {} 的容器 {} 失败: {}", userId, containerToRemove, e.getMessage());
                    runningContainers.remove(containerToRemove);
                }
            }
        }

        // 更新用户容器列表
        userContainers.put(userId, runningContainers);
    }

    /**
     * 确保多语言镜像存在
     */
    private void ensureMultiLanguageImageExists() throws Exception {
        String multiLangImage = dockerConfig.getContainerImage();
        if (!isImageExists(multiLangImage)) {
            log.info("多语言镜像 {} 不存在，开始拉取...", multiLangImage);
            pullImage(multiLangImage);
            log.info("多语言镜像 {} 拉取完成", multiLangImage);
        }
    }

    /**
     * 检查镜像是否存在
     */
    private boolean isImageExists(String imageName) {
        try {
            dockerClient.inspectImageCmd(imageName).exec();
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 拉取镜像
     */
    private void pullImage(String imageName) throws Exception {
        dockerClient.pullImageCmd(imageName)
                .exec(new ResultCallback.Adapter<PullResponseItem>() {
                    @Override
                    public void onNext(PullResponseItem item) {
                        if (item.getStatus() != null) {
                            log.info("拉取镜像进度: {}", item.getStatus());
                        }
                    }

                    @Override
                    public void onError(Throwable throwable) {
                        log.error("拉取镜像失败: {}", throwable.getMessage());
                    }

                    @Override
                    public void onComplete() {
                        log.info("镜像拉取完成: {}", imageName);
                    }
                }).awaitCompletion();
    }

    /**
     * 获取或创建用户专属多语言容器
     */
    private String getOrCreateMultiLanguageContainer(Long userId, CodeExecution codeExecution) throws Exception {
        // 使用分布式锁，确保同一用户的容器创建是原子操作
        String lockKey = "docker_user_lock_" + userId;
        RLock lock = redissonClient.getLock(lockKey);

        try {
            if (lock.tryLock(10, 30, TimeUnit.SECONDS)) {
                try {
                    // 再次检查用户是否已有容器（双重检查）
                    List<String> existingContainers = getUserContainerList(userId);

                    if (existingContainers != null && !existingContainers.isEmpty()) {
                        // 查找最新的运行中容器
                        for (int i = existingContainers.size() - 1; i >= 0; i--) {
                            String containerId = existingContainers.get(i);
                            if (isContainerRunning(containerId)) {
                                log.info("用户 {} 复用现有多语言容器: {}", userId, containerId);
                                return containerId;
                            }
                        }
                        // 如果没有运行中的容器，清理列表
                        existingContainers.clear();
                        userContainers.put(userId, existingContainers);
                    }

                    // 创建新的多语言容器
                    String newContainerId = createMultiLanguageContainer(userId, codeExecution);
                    log.info("用户 {} 创建新的多语言容器: {}", userId, newContainerId);

                    // 记录用户容器映射
                    List<String> userContainerList = getUserContainerList(userId);
                    userContainerList.add(newContainerId);
                    userContainers.put(userId, userContainerList);
                    containerCreateTimes.put(newContainerId, LocalDateTime.now());

                    return newContainerId;

                } finally {
                    lock.unlock();
                }
            } else {
                throw new RuntimeException("获取用户容器锁超时");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("获取用户容器锁被中断", e);
        }
    }

    /**
     * 创建多语言环境容器
     */
    private String createMultiLanguageContainer(Long userId, CodeExecution codeExecution) throws Exception {
        String multiLangImage = dockerConfig.getContainerImage();
        String containerName = "user-" + userId + "-multilang-" + System.currentTimeMillis();
        String workspacePath = "/home/<USER>";

        // 构建主机配置（资源限制）
        HostConfig hostConfig = HostConfig.newHostConfig()
                .withMemory(codeExecution.getMemoryLimitMB() * 1024L * 1024L) // 转换为字节
                .withCpuQuota((long) (codeExecution.getCpuLimit() * 100000)) // CPU配额
                .withCpuPeriod(100000L) // CPU周期
                .withNetworkMode(codeExecution.getNetworkAccess() ? "bridge" : "none") // 网络访问控制
                .withReadonlyRootfs(false) // 允许写入（代码执行需要）
                .withAutoRemove(false) // 不自动删除，我们手动管理
                .withBinds(Bind.parse("/etc/localtime:/etc/localtime:ro")); // 同步主机时区

        CreateContainerResponse container = dockerClient.createContainerCmd(multiLangImage)
                .withName(containerName)
                .withHostConfig(hostConfig)
                .withCmd("sh", "-c", "while true; do sleep 30; done") // 保持容器运行
                .withWorkingDir(workspacePath)
                .withTty(true) // 启用TTY，便于交互和日志输出
                .withStdinOpen(true) // 保持stdin开放
                .withEnv("LANG=C.UTF-8", "LC_ALL=C.UTF-8", "LC_CTYPE=C.UTF-8", "PYTHONIOENCODING=utf-8", "TZ=Asia/Shanghai")
                .exec();

        String containerId = container.getId();

        // 启动容器
        dockerClient.startContainerCmd(containerId).exec();

        log.info("多语言容器创建并启动成功: {} ({})", containerName, containerId);
        return containerId;
    }

    /**
     * 获取用户容器列表，处理类型兼容性
     */
    private List<String> getUserContainerList(Long userId) {
        List<String> userContainerObj = userContainers.get(userId);

        if (userContainerObj == null) {
            return new ArrayList<>();
        } else {
            return userContainerObj;
        }
    }

    @Override
    public List<String> getAvailablePythonPackages() {
        return new ArrayList<>(dockerConfig.getAvailablePythonPackages());
    }

    @Override
    public boolean isDockerAvailable() {
        try {
            return dockerConfig.isDockerAvailable();
        } catch (Exception e) {
            log.error("Docker服务不可用: {}", e.getMessage());
            return false;
        }
    }

    @Override
    public String getUserContainerId(Long userId) {
        List<String> containerList = getUserContainerList(userId);
        if (containerList != null && !containerList.isEmpty()) {
            // 返回最后一个（最新）运行中的容器
            for (int i = containerList.size() - 1; i >= 0; i--) {
                String containerId = containerList.get(i);
                if (isContainerRunning(containerId)) {
                    return containerId;
                }
            }
        }
        return null;
    }

    @Override
    public boolean removeUserContainer(Long userId) {
        List<String> containerList = getUserContainerList(userId);
        if (containerList != null && !containerList.isEmpty()) {
            boolean allRemoved = true;
            for (String containerId : new ArrayList<>(containerList)) {
                try {
                    dockerClient.stopContainerCmd(containerId).exec();
                    dockerClient.removeContainerCmd(containerId).exec();
                    containerCreateTimes.remove(containerId);
                    log.info("成功删除用户 {} 的容器: {}", userId, containerId);
                } catch (Exception e) {
                    log.error("删除用户 {} 的容器失败: {}, 错误: {}", userId, containerId, e.getMessage());
                    allRemoved = false;
                }
            }
            userContainers.remove(userId);
            return allRemoved;
        }
        return false;
    }

    @Override
    public Map<Long, String> getAllUserContainers() {
        Map<Long, String> result = new HashMap<>();
        for (Map.Entry<Long, List<String>> entry : userContainers.entrySet()) {
            List<String> containers = entry.getValue();
            if (containers != null && !containers.isEmpty()) {
                // 返回最后一个（最新）运行中的容器
                for (int i = containers.size() - 1; i >= 0; i--) {
                    String containerId = containers.get(i);
                    if (isContainerRunning(containerId)) {
                        result.put(entry.getKey(), containerId);
                        break;
                    }
                }
            }
        }
        return result;
    }

    @Override
    public String getContainerLogs(String containerId, Integer lines) {
        try {
            if (lines == null || lines <= 0) {
                lines = 100; // 默认100行
            }

            // 使用Docker API获取容器日志
            ByteArrayOutputStream stdout = new ByteArrayOutputStream();
            ByteArrayOutputStream stderr = new ByteArrayOutputStream();

            dockerClient.logContainerCmd(containerId)
                    .withStdOut(true)
                    .withStdErr(true)
                    .withTail(lines)
                    .withTimestamps(true)
                    .exec(new ExecStartResultCallback(stdout, stderr))
                    .awaitCompletion(10, TimeUnit.SECONDS);

            String stdoutStr = stdout.toString("UTF-8");
            String stderrStr = stderr.toString("UTF-8");

            StringBuilder logs = new StringBuilder();
            if (!stdoutStr.isEmpty()) {
                logs.append("=== STDOUT ===\n").append(stdoutStr).append("\n");
            }
            if (!stderrStr.isEmpty()) {
                logs.append("=== STDERR ===\n").append(stderrStr).append("\n");
            }

            return logs.toString();

        } catch (Exception e) {
            log.error("获取容器日志失败: {}, 错误: {}", containerId, e.getMessage());
            return "获取容器日志失败: " + e.getMessage();
        }
    }

    /**
     * 清理Redis中的无效容器数据
     * 定期调用此方法清理已停止但仍在Redis中的容器记录
     */
    public int cleanupInvalidRedisData() {
        int cleanedCount = 0;

        // 获取所有用户容器映射的副本
        Map<Long, List<String>> allUserContainers = new HashMap<>(userContainers);

        for (Map.Entry<Long, List<String>> entry : allUserContainers.entrySet()) {
            Long userId = entry.getKey();
            List<String> containerList = entry.getValue();

            if (containerList != null) {
                List<String> runningContainers = new ArrayList<>();
                for (String containerId : containerList) {
                    // 检查容器是否还在运行
                    if (isContainerRunning(containerId)) {
                        runningContainers.add(containerId);
                    } else {
                        // 容器已停止，清理时间记录
                        containerCreateTimes.remove(containerId);
                        cleanedCount++;
                        log.info("清理Redis中的无效容器数据 - 用户: {}, 容器: {}", userId, containerId);
                    }
                }

                // 更新用户容器列表
                if (runningContainers.isEmpty()) {
                    userContainers.remove(userId);
                } else {
                    userContainers.put(userId, runningContainers);
                }
            }
        }

        // 清理过期的执行结果
        LocalDateTime expireTime = LocalDateTime.now().minusHours(24);
        Map<String, ExecutionResult> allResults = new HashMap<>(executionResults);

        for (Map.Entry<String, ExecutionResult> entry : allResults.entrySet()) {
            String executionId = entry.getKey();
            ExecutionResult result = entry.getValue();

            if (result.getCreateTime() != null && result.getCreateTime().isBefore(expireTime)) {
                executionResults.remove(executionId);
                cleanedCount++;
                log.info("清理过期的执行结果: {}", executionId);
            }
        }

        log.info("Redis数据清理完成，清理了 {} 条无效记录", cleanedCount);
        return cleanedCount;
    }

    /**
     * 生成执行ID
     */
    private String generateExecutionId() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 16);
    }

    /**
     * 在容器中执行代码
     */
    private ExecutionResult executeCodeInContainer(String containerId, CodeExecution codeExecution) throws Exception {
        // 处理代码文件（支持直接输入代码和文件上传）
        String codeFilePath = handleCodeFile(containerId, codeExecution);

        // 构建执行命令，同时输出到容器日志
        String executeCommand;
        if (codeExecution.getInput() != null && !codeExecution.getInput().trim().isEmpty()) {
            // 有输入数据的情况
            // 有输入数据的情况 - 使用bash的PIPESTATUS
            executeCommand = String.format(
                    "bash -c '" +
                            "echo \"[$(date \"+%%Y-%%m-%%d %%H:%%M:%%S\")] 开始执行代码\" | tee /proc/1/fd/1; " +
                            "echo \"[$(date \"+%%Y-%%m-%%d %%H:%%M:%%S\")] 输入数据已设置\" | tee /proc/1/fd/1; " +
                            "cat << \"PYTHONINPUT\" | python %s 2>&1 | tee /proc/1/fd/1\n%s\nPYTHONINPUT; " +
                            "exit ${PIPESTATUS[1]}'" // 获取python命令的退出码
                    ,
                    codeFilePath,
                    codeExecution.getInput()
            );

        } else {
            // 无输入数据的情况
            executeCommand = String.format(
                    "bash -c '" +
                            "echo \"[$(date \"+%%Y-%%m-%%d %%H:%%M:%%S\")] 开始执行代码\" | tee /proc/1/fd/1; " +
                            "python %s 2>&1 | tee /proc/1/fd/1; " +
                            "exit ${PIPESTATUS[0]}'" // 获取python命令的退出码
                    ,
                    codeFilePath
            );
        }

        log.info("执行Shell命令: {}", executeCommand);

        long startTime = System.currentTimeMillis();
        ExecutionResult executionResult = execCommandWithExitCode(containerId, new String[]{"sh", "-c", executeCommand});
        long executionTime = System.currentTimeMillis() - startTime;
        executionResult.setExecutionTime(executionTime);
        return executionResult;
    }

    /**
     * 处理代码文件（统一处理直接输入代码和文件上传）
     */
    private String handleCodeFile(String containerId, CodeExecution codeExecution) throws Exception {
        String codeFileName;
        String containerFilePath;
        String workspacePath = dockerConfig.getWorkspacePath();

        if (codeExecution.getCode() != null && !codeExecution.getCode().trim().isEmpty()) {
            // 直接输入的代码
            codeFileName = "main.py";
            containerFilePath = workspacePath + "/" + codeFileName;

            // 创建代码文件并记录日志
            String createFileCmd = String.format(
                    "echo \"[$(date '+%%Y-%%m-%%d %%H:%%M:%%S')] 创建代码文件: %s\" | tee /proc/1/fd/1 && " +
                            "cat > %s << 'PYTHONCODE'\n%s\nPYTHONCODE\n" +
                            "echo \"[$(date '+%%Y-%%m-%%d %%H:%%M:%%S')] 代码文件创建完成，大小: $(wc -c < %s)字节\" | tee /proc/1/fd/1",
                    containerFilePath,
                    containerFilePath,
                    codeExecution.getCode(),
                    containerFilePath
            );
            execCommandInContainer(containerId, new String[]{"sh", "-c", createFileCmd});
            log.info("代码已写入文件: {}", containerFilePath);

        } else if (codeExecution.getCodeFilePath() != null && !codeExecution.getCodeFilePath().trim().isEmpty()) {
            // 文件上传的情况
            codeFileName = codeExecution.getCodeFileName() != null ? codeExecution.getCodeFileName() : "uploaded_code.py";
            containerFilePath = workspacePath + "/" + codeFileName;

            // 记录文件上传日志
            String logCmd = String.format("echo \"[$(date '+%%Y-%%m-%%d %%H:%%M:%%S')] 开始上传文件: %s\" | tee /proc/1/fd/1",
                    codeFileName);
            execCommandInContainer(containerId, new String[]{"sh", "-c", logCmd});

            // 将本地文件复制到容器中
            copyFileToContainer(containerId, codeExecution.getCodeFilePath(), containerFilePath);

            // 记录上传完成日志
            String completeLogCmd = String.format("echo \"[$(date '+%%Y-%%m-%%d %%H:%%M:%%S')] 文件上传完成: %s，大小: $(wc -c < %s)字节\" | tee /proc/1/fd/1",
                    codeFileName, containerFilePath);
            execCommandInContainer(containerId, new String[]{"sh", "-c", completeLogCmd});

            log.info("文件已复制到容器: {} -> {}", codeExecution.getCodeFilePath(), containerFilePath);

        } else {
            throw new RuntimeException("代码内容和文件路径都为空，请提供代码内容或上传文件");
        }

        return containerFilePath;
    }

    /**
     * 将本地文件复制到容器中
     */
    private void copyFileToContainer(String containerId, String localFilePath, String containerFilePath) throws Exception {
        // 读取本地文件内容
        Path path = Paths.get(localFilePath);
        if (!Files.exists(path)) {
            throw new RuntimeException("本地文件不存在: " + localFilePath);
        }

        // Java 8 兼容的文件读取方式
        byte[] bytes = Files.readAllBytes(path);
        String fileContent = new String(bytes, StandardCharsets.UTF_8);

        // 将文件内容写入容器
        String createFileCmd = String.format("cat > %s << 'EOF'\n%s\nEOF",
                containerFilePath, fileContent);
        execCommandInContainer(containerId, new String[]{"sh", "-c", createFileCmd});

        log.info("文件复制完成: {} -> {}", localFilePath, containerFilePath);
    }

    /**
     * 在容器中执行命令（公共方法，供DockerWorkspaceManager使用）
     */
    public void execCommandInContainer(String containerId, String[] command) throws Exception {
        ExecCreateCmdResponse execCreateCmdResponse = dockerClient.execCreateCmd(containerId)
                .withAttachStdout(true)
                .withAttachStderr(true)
                .withCmd(command)
                .exec();

        ByteArrayOutputStream stdout = new ByteArrayOutputStream();
        ByteArrayOutputStream stderr = new ByteArrayOutputStream();

        ExecStartResultCallback callback = new ExecStartResultCallback(stdout, stderr);
        dockerClient.execStartCmd(execCreateCmdResponse.getId())
                .exec(callback)
                .awaitCompletion(30, TimeUnit.SECONDS);

        String output = stdout.toString("UTF-8");
        String error = stderr.toString("UTF-8");

        // 合并stdout和stderr的输出，不要把stderr当作错误
        StringBuilder result = new StringBuilder();
        if (!output.isEmpty()) {
            result.append(output);
        }
        if (!error.isEmpty()) {
            if (result.length() > 0) {
                result.append("\n");
            }
            result.append(error);
        }

    }

    public ExecutionResult execCommandWithExitCode(String containerId, String[] command) throws Exception {
        ExecCreateCmdResponse execCreateCmdResponse = dockerClient.execCreateCmd(containerId)
                .withAttachStdout(true)
                .withAttachStderr(true)
                .withCmd(command)
                .exec();

        ByteArrayOutputStream stdout = new ByteArrayOutputStream();
        ByteArrayOutputStream stderr = new ByteArrayOutputStream();

        // 使用匿名内部类替代已弃用的ExecStartResultCallback
        ResultCallback.Adapter<com.github.dockerjava.api.model.Frame> callback =
                new ResultCallback.Adapter<com.github.dockerjava.api.model.Frame>() {
                    @Override
                    public void onStart(Closeable closeable) {
                        log.info("Docker命令已开始执行");
                    }

                    @Override
                    public void onNext(com.github.dockerjava.api.model.Frame item) {
                        if (item.getStreamType() == com.github.dockerjava.api.model.StreamType.STDOUT) {
                            try {
                                stdout.write(item.getPayload());
                            } catch (Exception e) {
                                log.error("写入stdout时出错", e);
                            }
                        } else if (item.getStreamType() == com.github.dockerjava.api.model.StreamType.STDERR) {
                            try {
                                stderr.write(item.getPayload());
                            } catch (Exception e) {
                                log.error("写入stderr时出错", e);
                            }
                        }
                        super.onNext(item);
                    }

                    @Override
                    public void onComplete() {
                        log.info("Docker命令执行完成");
                        super.onComplete();
                    }

                    @Override
                    public void onError(Throwable throwable) {
                        log.error("执行Docker命令时出错", throwable);
                        super.onError(throwable);
                    }
                };

        // 等待命令执行完成
        dockerClient.execStartCmd(execCreateCmdResponse.getId())
                .exec(callback)
                .awaitCompletion(180, TimeUnit.SECONDS);


        log.info("Docker命令执行完成 stdout: {}, stderr: {}", stdout, stderr);

        // 创建执行结果
        ExecutionResult execResult = new ExecutionResult();
        String output = stdout.toString("UTF-8");
        String error = stderr.toString("UTF-8");


        // 综合判断：内容检查
        if (containsError(output) || containsError(error)) {
            // 有错误：内容包含错误信息
            execResult.setStderr(error.isEmpty() ? output : error + "\n" + output);
            execResult.setErrorMessage(extractErrorFromOutput(execResult.getStderr()));
            execResult.setStatus("ERROR");
        } else {
            // 成功执行
            execResult.setStdout(output);
            execResult.setStatus("SUCCESS");
        }

        return execResult;
    }

    /**
     * 从输出中提取错误信息
     */
    private String extractErrorFromOutput(String output) {
        if (output == null || output.trim().isEmpty()) {
            return "执行失败";
        }

        // 查找最后一个错误行
        String[] lines = output.split("\n");
        for (int i = lines.length - 1; i >= 0; i--) {
            String line = lines[i].trim();
            if (line.matches(".*Error:.*") || line.matches(".*Exception:.*")) {
                return line;
            }
        }

        return "执行出现错误";
    }

    // 使用正则表达式来增强错误识别
    private boolean containsError(String input) {
        // 常见的错误模式（正则表达式）
        String[] errorPatterns = {
                "Traceback",      // Python 错误栈
                "SyntaxError",    // Python 语法错误
                "Error",          // 常见的错误关键字
                "Exception",      // Java 异常
                "callback",       // 其他回调错误
                "Permission denied",  // 权限错误
                "No such file or directory",  // 文件未找到
                "Connection refused",  // 网络连接错误
                "MemoryError",    // 内存错误
                "OutOfMemoryError", // Java内存溢出
                "Segmentation fault",  // 段错误
                "Killed",           // 进程被杀死
                "Timeout",          // 超时错误
                "Fail"              // 一些失败类型
        };

        for (String pattern : errorPatterns) {
            if (Pattern.compile(pattern, Pattern.CASE_INSENSITIVE).matcher(input).find()) {
                return true;  // 如果匹配到任何一个错误模式，返回 true
            }
        }
        return false;
    }


    /**
     * 安装Python包
     */
    private void installPythonPackages(String containerId, List<String> packages) {
        List<String> availablePackages = dockerConfig.getAvailablePythonPackages();
        for (String packageName : packages) {
            if (availablePackages.contains(packageName)) {
                try {
                    log.info("正在安装Python包: {}", packageName);
                    String installCmd = "pip install " + packageName;
                    execCommandInContainer(containerId, new String[]{"sh", "-c", installCmd});
                    log.info("Python包安装成功: {}", packageName);
                } catch (Exception e) {
                    log.warn("Python包安装失败: {}, 错误: {}", packageName, e.getMessage());
                }
            } else {
                log.warn("不支持的Python包: {}", packageName);
            }
        }
    }

    /**
     * 获取容器统计信息
     */
    private Statistics getContainerStats(String containerId) {
        try {
            // 简化统计信息获取，避免复杂的API调用
            // 在生产环境中可以实现更详细的统计信息收集
            log.info("跳过容器统计信息收集: {}", containerId);
            return null; // 暂时返回null，避免API兼容性问题
        } catch (Exception e) {
            log.warn("获取容器统计信息失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 计算CPU使用率
     */
    private Double calculateCpuUsage(Statistics stats) {
        try {
            // 简化CPU使用率计算，避免复杂的API调用
            if (stats == null || stats.getCpuStats() == null) {
                return 0.0;
            }

            // 返回一个简单的CPU使用率估算
            // 在实际生产环境中，可以根据需要实现更精确的计算
            return 0.0;
        } catch (Exception e) {
            log.warn("计算CPU使用率失败: {}", e.getMessage());
            return 0.0;
        }
    }

    /**
     * 调度容器销毁
     */
    private void scheduleContainerDestroy(String containerId, String executionId) {
        if (containerId == null) return;

        Timer timer = new Timer();
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                try {
                    dockerClient.removeContainerCmd(containerId).withForce(true).exec();
                    ExecutionResult result = executionResults.get(executionId);
                    if (result != null) {
                        result.setDestroyTime(LocalDateTime.now());
                    }
                    log.info("定时销毁容器: {}", containerId);
                } catch (Exception e) {
                    log.warn("定时销毁容器失败: {}, 错误: {}", containerId, e.getMessage());
                }
            }
        }, TimeUnit.HOURS.toMillis(dockerConfig.getContainerLifetimeHours()));
    }


    /**
     * 设置容器日志环境
     */
    private void setupContainerLogging(String containerId) {
        try {
            // 创建日志记录脚本
            String logScript = "#!/bin/bash\n" +
                    "# 容器日志记录脚本\n" +
                    "echo \"[$(date '+%Y-%m-%d %H:%M:%S')] 容器启动完成，ID: " + containerId + "\"\n" +
                    "echo \"[$(date '+%Y-%m-%d %H:%M:%S')] 工作目录: $(pwd)\"\n" +
                    "echo \"[$(date '+%Y-%m-%d %H:%M:%S')] Python版本: $(python --version 2>&1)\"\n" +
                    "echo \"[$(date '+%Y-%m-%d %H:%M:%S')] 容器准备就绪，等待代码执行...\"\n";

            // 将脚本写入容器
            String createScriptCmd = String.format("cat > /home/<USER>/container_log.sh << 'EOF'\n%sEOF", logScript);
            execCommandInContainer(containerId, new String[]{"sh", "-c", createScriptCmd});

            // 设置脚本执行权限
            execCommandInContainer(containerId, new String[]{"chmod", "+x", "/home/<USER>/container_log.sh"});

            // 执行日志脚本，输出到容器日志
            execCommandInContainer(containerId, new String[]{"sh", "-c", "sh /home/<USER>/container_log.sh | tee /proc/1/fd/1"});

            log.info("容器日志环境设置完成: {}", containerId);

        } catch (Exception e) {
            log.warn("设置容器日志环境失败: {}, 错误: {}", containerId, e.getMessage());
        }
    }

}

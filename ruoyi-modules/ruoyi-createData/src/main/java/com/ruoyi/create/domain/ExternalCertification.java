package com.ruoyi.create.domain;

import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExternalCertification extends BaseEntity {
    private Long userId;
    private Integer isStuOrTea;// 1 Stu/0 Tea

    private String teacherId;
    private String studentId;

    private String sex;//
    private Integer sexMark;//

    private String name;

    private Long univerId;//
    private String univerName;//

    private Long colleId;//
    private String colleName;//

    private Long majorId;//
    private String majorName;//

    private Long classId;//
    private String className;//


    private String createBy;
    private Date createTime;
    private String updateBy;
    private Date updateTime;
}

package com.ruoyi.create.executor.impl;

import com.ruoyi.create.domain.CodeExecution;
import com.ruoyi.create.domain.ExecutionResult;
import com.ruoyi.create.executor.AbstractLanguageExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Java代码执行器
 *
 * <AUTHOR>
 * @date 2024-07-31
 */
@Slf4j
@Component
public class JavaExecutor extends AbstractLanguageExecutor {

    @Override
    public String getSupportedLanguage() {
        return "java";
    }

    @Override
    public String getDockerImage() {
        return "multi-lang-executor:latest";
    }

    @Override
    public String getFileExtension() {
        return ".java";
    }

    @Override
    public String getDefaultFileName() {
        return "Main.java";
    }

    @Override
    public String[] getExecuteCommand(String codeFilePath, String input) {
        // Java需要先编译再执行
        String className = extractClassName(codeFilePath);
        return new String[]{"sh", "-c",
            String.format("cd $(dirname %s) && javac %s && java %s",
                         codeFilePath, codeFilePath, className)};
    }

    @Override
    protected ExecutionResult executeCodeInContainer(String containerId,
                                                   CodeExecution codeExecution,
                                                   String codeFilePath,
                                                   com.ruoyi.create.service.impl.DockerExecutionServiceImpl executionService) throws Exception {

        // 提取类名
        String className = extractClassName(codeFilePath);
        String workDir = "/home/<USER>";

        // 构建编译和执行命令
        String compileAndRunCmd = buildJavaExecuteCommand(codeFilePath, className, codeExecution.getInput());

        log.info("执行Java编译和运行命令: {}", compileAndRunCmd);

        long startTime = System.currentTimeMillis();
        ExecutionResult executionResult = executionService.execCommandWithExitCode(containerId, new String[]{"sh", "-c", compileAndRunCmd});
        long executionTime = System.currentTimeMillis() - startTime;
        executionResult.setExecutionTime(executionTime);

        return executionResult;
    }

    /**
     * 构建Java编译和执行命令
     */
    private String buildJavaExecuteCommand(String codeFilePath, String className, String input) {
        StringBuilder cmdBuilder = new StringBuilder();

        // 添加开始日志
        cmdBuilder.append("echo \"[$(date \"+%Y-%m-%d %H:%M:%S\")] 开始编译Java代码 \" | tee /proc/1/fd/1; ");

        // 编译Java代码
        cmdBuilder.append("cd $(dirname ").append(codeFilePath).append(") && ");
        cmdBuilder.append("javac ").append(codeFilePath).append(" 2>&1 | tee /proc/1/fd/1; ");

        // 检查编译是否成功
        cmdBuilder.append("if [ $? -eq 0 ]; then ");
        cmdBuilder.append("echo \"[$(date \"+%Y-%m-%d %H:%M:%S\")] Java编译成功，开始执行 \" | tee /proc/1/fd/1; ");

        // 如果有输入数据，记录输入
        if (input != null && !input.trim().isEmpty()) {
            cmdBuilder.append("echo \"[$(date \"+%Y-%m-%d %H:%M:%S\")] 输入数据已设置\" | tee /proc/1/fd/1; ");

            // 执行Java程序（带输入）
            cmdBuilder.append("cat << 'JAVAINPUT' | java ").append(className).append(" 2>&1 | tee /proc/1/fd/1\n")
                     .append(input).append("\nJAVAINPUT; ");
        } else {
            // 执行Java程序（无输入）
            cmdBuilder.append("java ").append(className).append(" 2>&1 | tee /proc/1/fd/1; ");
        }

        cmdBuilder.append("else ");
        cmdBuilder.append("echo \"[$(date \"+%Y-%m-%d %H:%M:%S\")] Java编译失败 \" | tee /proc/1/fd/1; ");
        cmdBuilder.append("exit 1; ");
        cmdBuilder.append("fi");

        return cmdBuilder.toString();
    }

    /**
     * 从代码文件路径或代码内容中提取类名
     */
    private String extractClassName(String codeFilePath) {
        // 从文件路径提取类名
        String fileName = codeFilePath.substring(codeFilePath.lastIndexOf('/') + 1);
        if (fileName.endsWith(".java")) {
            return fileName.substring(0, fileName.length() - 5);
        }
        return "Main";
    }

    @Override
    public String handleCodeFile(String containerId,
                                CodeExecution codeExecution,
                                String workspacePath,
                                com.ruoyi.create.service.impl.DockerExecutionServiceImpl executionService) throws Exception {

        String className = extractClassNameFromCode(codeExecution.getCode());
        String codeFileName = className + ".java";
        String containerFilePath = workspacePath + "/" + codeFileName;

        if (codeExecution.getCode() != null && !codeExecution.getCode().trim().isEmpty()) {
            // 直接输入的代码
            String createFileCmd = String.format(
                    "echo \"[$(date '+%%Y-%%m-%%d %%H:%%M:%%S')] 创建Java代码文件: %s\" | tee /proc/1/fd/1 && " +
                            "cat > %s << 'JAVACODE'\n%s\nJAVACODE\n" +
                            "echo \"[$(date '+%%Y-%%m-%%d %%H:%%M:%%S')] Java代码文件创建完成，大小: $(wc -c < %s)字节\" | tee /proc/1/fd/1",
                    containerFilePath,
                    containerFilePath,
                    codeExecution.getCode(),
                    containerFilePath
            );
            executionService.execCommandInContainer(containerId, new String[]{"sh", "-c", createFileCmd});
            log.debug("Java代码已写入文件: {}", containerFilePath);

        } else if (codeExecution.getCodeFilePath() != null && !codeExecution.getCodeFilePath().trim().isEmpty()) {
            // 文件上传的情况
            copyFileToContainer(containerId, codeExecution.getCodeFilePath(), containerFilePath, executionService);
            log.debug("Java文件已复制到容器: {} -> {}", codeExecution.getCodeFilePath(), containerFilePath);
        } else {
            throw new RuntimeException("代码内容和文件路径都为空，请提供代码内容或上传文件");
        }

        return containerFilePath;
    }

    /**
     * 从Java代码中提取类名
     */
    private String extractClassNameFromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return "Main";
        }

        // 使用正则表达式查找public class声明
        Pattern pattern = Pattern.compile("public\\s+class\\s+(\\w+)");
        Matcher matcher = pattern.matcher(code);

        if (matcher.find()) {
            return matcher.group(1);
        }

        // 如果没找到public class，查找普通class
        pattern = Pattern.compile("class\\s+(\\w+)");
        matcher = pattern.matcher(code);

        if (matcher.find()) {
            return matcher.group(1);
        }

        return "Main";
    }

    @Override
    public String validateSyntax(String code) {
        if (code == null || code.trim().isEmpty()) {
            return "代码内容不能为空";
        }

        // 检查是否包含类声明
        if (!code.contains("class ")) {
            return "Java代码必须包含类声明";
        }

        // 检查是否包含main方法
        if (!code.contains("public static void main")) {
            return "Java代码必须包含main方法";
        }

        // 检查基本的括号匹配
        int braceCount = 0;
        for (char c : code.toCharArray()) {
            if (c == '{') braceCount++;
            if (c == '}') braceCount--;
            if (braceCount < 0) {
                return "括号不匹配：多余的右括号";
            }
        }

        if (braceCount != 0) {
            return "括号不匹配：缺少" + (braceCount > 0 ? "右" : "左") + "括号";
        }

        return null; // 验证通过
    }

    @Override
    public void installPackages(String containerId,
                               List<String> packages,
                               com.ruoyi.create.service.impl.DockerExecutionServiceImpl executionService) {
        // Java通常不需要运行时安装包，依赖通过Maven/Gradle管理
        // 这里可以实现Maven依赖下载逻辑，但为了简化暂不实现
        log.info("Java执行器暂不支持运行时包安装，请使用Maven/Gradle管理依赖");
    }
}

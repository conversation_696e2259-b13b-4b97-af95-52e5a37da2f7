package com.ruoyi.create.executor.impl;

import com.ruoyi.create.executor.AbstractLanguageExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Python代码执行器
 *
 * <AUTHOR>
 * @date 2024-07-31
 */
@Slf4j
@Component
public class PythonExecutor extends AbstractLanguageExecutor {

    @Override
    public String getSupportedLanguage() {
        return "python";
    }

    @Override
    public String getDockerImage() {
        return "multi-lang-executor:latest"; // 完整版镜像，包含numpy, pandas, tensorflow等
    }

    @Override
    public String getFileExtension() {
        return ".py";
    }

    @Override
    public String getDefaultFileName() {
        return "main.py";
    }

    @Override
    public String[] getExecuteCommand(String codeFilePath, String input) {
        return new String[]{"python3", codeFilePath};
    }

    @Override
    public void installPackages(String containerId, 
                               List<String> packages,
                               com.ruoyi.create.service.impl.DockerExecutionServiceImpl executionService) {
        if (packages == null || packages.isEmpty()) {
            return;
        }

        for (String packageName : packages) {
            try {
                log.info("正在安装Python包: {}", packageName);
                String installCmd = String.format(
                    "echo \"[$(date '+%%Y-%%m-%%d %%H:%%M:%%S')] 开始安装Python包: %s\" | tee /proc/1/fd/1 && " +
                    "pip3 install %s && " +
                    "echo \"[$(date '+%%Y-%%m-%%d %%H:%%M:%%S')] Python包安装成功: %s\" | tee /proc/1/fd/1",
                    packageName, packageName, packageName
                );
                executionService.execCommandInContainer(containerId, new String[]{"sh", "-c", installCmd});
                log.info("Python包安装成功: {}", packageName);
            } catch (Exception e) {
                log.warn("Python包安装失败: {}, 错误: {}", packageName, e.getMessage());
                // 继续安装其他包，不中断流程
            }
        }
    }

    @Override
    public String validateSyntax(String code) {
        // 简单的Python语法检查
        if (code == null || code.trim().isEmpty()) {
            return "代码内容不能为空";
        }

        // 检查基本的Python语法错误
        if (code.contains("print(") && !code.contains(")")) {
            return "print语句语法错误：缺少右括号";
        }

        // 检查缩进问题（简单检查）
        String[] lines = code.split("\n");
        for (int i = 0; i < lines.length; i++) {
            String line = lines[i];
            if (line.trim().endsWith(":") && i + 1 < lines.length) {
                String nextLine = lines[i + 1];
                if (!nextLine.trim().isEmpty() && !nextLine.startsWith("    ") && !nextLine.startsWith("\t")) {
                    return "第" + (i + 2) + "行缩进错误：冒号后的代码块需要缩进";
                }
            }
        }

        return null; // 验证通过
    }
}

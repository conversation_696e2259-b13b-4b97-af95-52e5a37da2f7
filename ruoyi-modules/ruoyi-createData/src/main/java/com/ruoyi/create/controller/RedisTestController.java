package com.ruoyi.create.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.create.domain.ExecutionResult;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Redis缓存测试控制器 - 验证JSON序列化配置
 * 
 * <AUTHOR>
 * @date 2024-08-05
 */
@RestController
@RequestMapping("/test/redis")
@Slf4j
public class RedisTestController extends BaseController {

    @Resource
    private RedissonClient redissonClient;

    /**
     * 测试Redis连接和序列化
     */
    @GetMapping("/test-serialization")
    public AjaxResult testSerialization() {
        try {
            Map<String, Object> result = new HashMap<>();
            
            // 测试ExecutionResult序列化
            RMap<String, ExecutionResult> executionMap = redissonClient.getMap("test:execution");
            ExecutionResult testResult = new ExecutionResult()
                    .setExecutionId("test-" + System.currentTimeMillis())
                    .setStatus("SUCCESS")
                    .setStdout("Hello World! 测试中文输出")
                    .setStartTime(LocalDateTime.now())
                    .setCreateTime(LocalDateTime.now());
            
            String key1 = "test-exec-" + System.currentTimeMillis();
            executionMap.put(key1, testResult);
            ExecutionResult retrieved1 = executionMap.get(key1);
            result.put("executionResult", retrieved1 != null && testResult.getExecutionId().equals(retrieved1.getExecutionId()));
            
            // 测试List序列化
            RMap<String, List<String>> listMap = redissonClient.getMap("test:list");
            List<String> testList = Arrays.asList("container1", "容器2", "container3");
            String key2 = "test-list-" + System.currentTimeMillis();
            listMap.put(key2, testList);
            List<String> retrieved2 = listMap.get(key2);
            result.put("listSerialization", retrieved2 != null && retrieved2.size() == 3);
            
            // 测试LocalDateTime序列化
            RMap<String, LocalDateTime> timeMap = redissonClient.getMap("test:time");
            LocalDateTime testTime = LocalDateTime.now();
            String key3 = "test-time-" + System.currentTimeMillis();
            timeMap.put(key3, testTime);
            LocalDateTime retrieved3 = timeMap.get(key3);
            result.put("dateTimeSerialization", retrieved3 != null && testTime.equals(retrieved3));
            
            // 清理测试数据
            executionMap.remove(key1);
            listMap.remove(key2);
            timeMap.remove(key3);
            
            result.put("allTestsPassed", 
                (Boolean)result.get("executionResult") && 
                (Boolean)result.get("listSerialization") && 
                (Boolean)result.get("dateTimeSerialization"));
            
            log.info("Redis序列化测试完成: {}", result);
            return AjaxResult.success("Redis序列化测试完成", result);
            
        } catch (Exception e) {
            log.error("Redis序列化测试失败: {}", e.getMessage(), e);
            return AjaxResult.error("测试失败: " + e.getMessage());
        }
    }

    /**
     * 获取Redis信息
     */
    @GetMapping("/info")
    public AjaxResult getRedisInfo() {
        try {
            Map<String, Object> info = new HashMap<>();
            info.put("connected", true);
            info.put("keysCount", redissonClient.getKeys().count());
            info.put("timestamp", LocalDateTime.now());
            
            return AjaxResult.success("Redis信息获取成功", info);
        } catch (Exception e) {
            log.error("获取Redis信息失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取Redis信息失败: " + e.getMessage());
        }
    }

    /**
     * 清理测试数据
     */
    @DeleteMapping("/cleanup")
    public AjaxResult cleanup() {
        try {
            redissonClient.getMap("test:execution").clear();
            redissonClient.getMap("test:list").clear();
            redissonClient.getMap("test:time").clear();
            
            log.info("Redis测试数据清理完成");
            return AjaxResult.success("测试数据清理完成");
        } catch (Exception e) {
            log.error("清理测试数据失败: {}", e.getMessage(), e);
            return AjaxResult.error("清理失败: " + e.getMessage());
        }
    }
}

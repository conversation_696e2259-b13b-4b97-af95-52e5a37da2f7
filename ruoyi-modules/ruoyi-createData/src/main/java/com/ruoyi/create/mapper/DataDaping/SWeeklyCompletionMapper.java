package com.ruoyi.create.mapper.DataDaping;

import com.ruoyi.create.domain.DataDaping.SWeeklyCompletion;

import java.util.List;


/**
 * 学生作业周完成情况Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-08
 */
public interface SWeeklyCompletionMapper 
{
    List<SWeeklyCompletion> selectLatestWeeklyCompletionList();

    /**
     * 查询学生作业周完成情况
     * 
     * @param id 学生作业周完成情况主键
     * @return 学生作业周完成情况
     */
    public SWeeklyCompletion selectSWeeklyCompletionById(Long id);

    /**
     * 查询学生作业周完成情况列表
     * 
     * @param sWeeklyCompletion 学生作业周完成情况
     * @return 学生作业周完成情况集合
     */
    public List<SWeeklyCompletion> selectSWeeklyCompletionList(SWeeklyCompletion sWeeklyCompletion);

    /**
     * 新增学生作业周完成情况
     * 
     * @param sWeeklyCompletion 学生作业周完成情况
     * @return 结果
     */
    public int insertSWeeklyCompletion(SWeeklyCompletion sWeeklyCompletion);

    /**
     * 修改学生作业周完成情况
     * 
     * @param sWeeklyCompletion 学生作业周完成情况
     * @return 结果
     */
    public int updateSWeeklyCompletion(SWeeklyCompletion sWeeklyCompletion);

    /**
     * 删除学生作业周完成情况
     * 
     * @param id 学生作业周完成情况主键
     * @return 结果
     */
    public int deleteSWeeklyCompletionById(Long id);

    /**
     * 批量删除学生作业周完成情况
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSWeeklyCompletionByIds(Long[] ids);
}

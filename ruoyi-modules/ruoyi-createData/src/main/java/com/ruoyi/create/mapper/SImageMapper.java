package com.ruoyi.create.mapper;

import com.ruoyi.create.domain.SImage;

import java.util.List;

/**
 * 系统图片Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-05
 */
public interface SImageMapper 
{
    /**
     * 查询系统图片
     * 
     * @param id 系统图片主键
     * @return 系统图片
     */
    public SImage selectSImageById(Long id);

    /**
     * 查询系统图片列表
     * 
     * @param sImage 系统图片
     * @return 系统图片集合
     */
    public List<SImage> selectSImageList(SImage sImage);

    /**
     * 新增系统图片
     * 
     * @param sImage 系统图片
     * @return 结果
     */
    public int insertSImage(SImage sImage);

    /**
     * 修改系统图片
     * 
     * @param sImage 系统图片
     * @return 结果
     */
    public int updateSImage(SImage sImage);

    /**
     * 删除系统图片
     * 
     * @param id 系统图片主键
     * @return 结果
     */
    public int deleteSImageById(Long id);

    /**
     * 批量删除系统图片
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSImageByIds(Long[] ids);

    List<SImage> selectImageAll();
}

package com.ruoyi.create.domain;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 课程班级信息统计对象 s_class_student_course_report
 *
 * <AUTHOR>
 * @date 2024-12-11
 */
@Data
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class ClassStudentCourseReport extends BaseEntity {
	private static final long serialVersionUID = 1L;

	/**
	 *
	 */
	private Long id;

	/**
	 * student  学生统计/ class 班级统计
	 */
	@Excel(name = "student  学生统计/ class 班级统计")
	private String type;

	/**
	 * 课程班级id
	 */
	@Excel(name = "课程班级id")
	private Long courseClassId;

	/**
	 * 课程班级名字
	 */
	@Excel(name = "课程班级名字")
	private String courseClassName;

	/**
	 * 学号
	 */
	@Excel(name = "学号")
	private String studentId;

	/**
	 * 用户id
	 */
	@Excel(name = "用户id")
	private Long userId;

	/**
	 * 昵称
	 */
	@Excel(name = "昵称")
	private String nickName;

	/**
	 * 课程名
	 */
	@Excel(name = "课程名")
	private String courseName;

	/**
	 * 报告日期
	 */
	@JsonFormat(pattern = "yyyy-MM-dd")
	@Excel(name = "报告日期", width = 30, dateFormat = "yyyy-MM-dd")
	private Date reportDate;

	/**
	 * 总章节
	 */
	@Excel(name = "总章节")
	private String totalChapter;

	/**
	 * 已完成章节
	 */
	@Excel(name = "已完成章节")
	private String finishedChapter;

	/**
	 * 完成进度 (百分比)
	 */
	@Excel(name = "完成进度 (百分比)")
	private String completionProgress;

	/**
	 * 平均完成进度
	 */
	@Excel(name = "平均完成进度")
	private String averageCompletionProgress;

	/**
	 * 与平均进度比较
	 */
	@Excel(name = "与平均进度比较")
	private String comparison;

	/**
	 * 智能问答次数
	 */
	@Excel(name = "智能问答次数")
	private String intelligentQuestionAnsweringCount;

	/**
	 * 平均每周问答次数
	 */
	@Excel(name = "平均每周问答次数")
	private String averageWeeklyQuestionAnsweringCount;

	/**
	 * 班级平均问答次数
	 */
	@Excel(name = "班级平均问答次数")
	private String classAverageQuestionAnsweringCount;

	/**
	 * 与班级平均问答次数比较
	 */
	@Excel(name = "与班级平均问答次数比较")
	private String comparisonWithClassAverageQuestionAnsweringCount;

	/**
	 * 总作业数
	 */
	@Excel(name = "总作业数")
	private String totalHomework;

	/**
	 * 已提交次数
	 */
	@Excel(name = "已提交次数")
	private String submittedTimes;

	/**
	 * 提交比例
	 */
	@Excel(name = "提交比例")
	private String submissionRatio;

	/**
	 * 学习进度建议
	 */
	@Excel(name = "学习进度建议")
	private String learningProgressAdvice;

	/**
	 * 提问频次建议
	 */
	@Excel(name = "提问频次建议")
	private String frequencyOfQuestionsAdvice;

	/**
	 * 学习策略建议
	 */
	@Excel(name = "学习策略建议")
	private String learningStrategyAdvice;

	/**
	 * 完成进度领先的学生比例
	 */
	@Excel(name = "完成进度领先的学生比例")
	private String countLeadingRatio;

	/**
	 * 完成进度持平的学生比例
	 */
	@Excel(name = "完成进度持平的学生比例")
	private String countFlatRatio;

	/**
	 * 完成进度落后的学生比例
	 */
	@Excel(name = "完成进度落后的学生比例")
	private String countLagRatio;

	/**
	 * 作业完成百分之百比例
	 */
	@Excel(name = "作业完成百分之百比例")
	private String countP100;

	/**
	 * 作业完成百分之90比例
	 */
	@Excel(name = "作业完成百分之90比例")
	private String countP9;

	/**
	 * 作业完成百分之60比例
	 */
	@Excel(name = "作业完成百分之60比例")
	private String countP6;

	/**
	 * 作业提交低于60%的学生姓名
	 */
	@Excel(name = "作业提交低于60%的学生姓名")
	private String lessThanP6NameStr;

	/**
	 * 问答次数高于90%的比例
	 */
	@Excel(name = "问答次数高于90%的比例")
	private String quoteAnswerCountP9;

	/**
	 * 问答次数高于60%的比例
	 */
	@Excel(name = "问答次数高于60%的比例")
	private String quoteAnswerCountP6;

	/**
	 * 问答次数低于 60% 学生姓名
	 */
	@Excel(name = "问答次数低于 60% 学生姓名")
	private String quoteAnswerLessThantStr;

	/**
	 * 班级学习进度建议
	 */
	@Excel(name = "班级学习进度建议")
	private String suggestionsForClassProgress;

	/**
	 * 班级课后作业建议
	 */
	@Excel(name = "班级课后作业建议")
	private String suggestionsForClassHomework;

	/**
	 * 班级问答互动建议
	 */
	@Excel(name = "班级问答互动建议")
	private String suggestionsForClassInteractions;


}

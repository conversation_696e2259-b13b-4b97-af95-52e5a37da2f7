package com.ruoyi.create.executor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 语言执行器工厂
 * 负责管理和提供不同编程语言的执行器
 *
 * <AUTHOR>
 * @date 2024-07-31
 */
@Slf4j
@Component
public class LanguageExecutorFactory {

    @Autowired
    private List<LanguageExecutor> executors;

    private final Map<String, LanguageExecutor> executorMap = new HashMap<>();

    /**
     * 初始化执行器映射
     */
    @PostConstruct
    public void init() {
        for (LanguageExecutor executor : executors) {
            String language = executor.getSupportedLanguage().toLowerCase();
            executorMap.put(language, executor);
            log.info("注册语言执行器: {} -> {}", language, executor.getClass().getSimpleName());
        }

        log.info("语言执行器工厂初始化完成，支持的语言: {}", getSupportedLanguages());
    }

    /**
     * 根据编程语言获取对应的执行器
     *
     * @param language 编程语言名称（不区分大小写）
     * @return 语言执行器
     * @throws RuntimeException 如果不支持该语言
     */
    public LanguageExecutor getExecutor(String language) {
        if (language == null || language.trim().isEmpty()) {
            throw new RuntimeException("编程语言不能为空");
        }

        String normalizedLanguage = normalizeLanguage(language.toLowerCase().trim());
        LanguageExecutor executor = executorMap.get(normalizedLanguage);

        if (executor == null) {
            throw new RuntimeException("不支持的编程语言: " + language +
                                     "，支持的语言: " + getSupportedLanguages());
        }

        return executor;
    }

    /**
     * 检查是否支持指定的编程语言
     *
     * @param language 编程语言名称
     * @return 是否支持
     */
    public boolean isLanguageSupported(String language) {
        if (language == null || language.trim().isEmpty()) {
            return false;
        }

        String normalizedLanguage = normalizeLanguage(language.toLowerCase().trim());
        return executorMap.containsKey(normalizedLanguage);
    }

    /**
     * 获取所有支持的编程语言
     *
     * @return 支持的语言集合
     */
    public Set<String> getSupportedLanguages() {
        return executorMap.keySet();
    }

    /**
     * 获取指定语言的Docker镜像名称
     *
     * @param language 编程语言名称
     * @return Docker镜像名称
     */
    public String getDockerImage(String language) {
        LanguageExecutor executor = getExecutor(language);
        return executor.getDockerImage();
    }

    /**
     * 验证代码语法
     *
     * @param language 编程语言
     * @param code 代码内容
     * @return 验证结果，null表示验证通过
     */
    public String validateCodeSyntax(String language, String code) {
        try {
            LanguageExecutor executor = getExecutor(language);
            return executor.validateSyntax(code);
        } catch (Exception e) {
            return "语法验证失败: " + e.getMessage();
        }
    }

    /**
     * 标准化语言名称
     * 处理常见的语言名称变体
     */
    private String normalizeLanguage(String language) {
        switch (language) {
            case "js":
            case "node":
            case "nodejs":
                return "javascript";
            case "c++":
            case "cxx":
                return "cpp";
            case "py":
            case "python3":
                return "python";
            default:
                return language;
        }
    }

    /**
     * 获取语言的文件扩展名
     *
     * @param language 编程语言名称
     * @return 文件扩展名
     */
    public String getFileExtension(String language) {
        LanguageExecutor executor = getExecutor(language);
        return executor.getFileExtension();
    }

    /**
     * 获取语言的默认文件名
     *
     * @param language 编程语言名称
     * @return 默认文件名
     */
    public String getDefaultFileName(String language) {
        LanguageExecutor executor = getExecutor(language);
        return executor.getDefaultFileName();
    }

    /**
     * 获取执行器统计信息
     *
     * @return 执行器统计信息
     */
    public Map<String, Object> getExecutorStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalExecutors", executorMap.size());
        stats.put("supportedLanguages", getSupportedLanguages());

        Map<String, String> languageToExecutor = new HashMap<>();
        for (Map.Entry<String, LanguageExecutor> entry : executorMap.entrySet()) {
            languageToExecutor.put(entry.getKey(), entry.getValue().getClass().getSimpleName());
        }
        stats.put("languageExecutorMapping", languageToExecutor);

        return stats;
    }
}

package com.ruoyi.create.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.config.DockerConfig;
import com.ruoyi.create.domain.CodeExecution;
import com.ruoyi.create.domain.ExecutionResult;
import com.ruoyi.create.service.IDockerExecutionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 代码执行控制器
 * 提供在线代码执行和评测功能
 *
 * <AUTHOR>
 * @date 2024-07-30
 */
@Slf4j
@Api(tags = "代码执行管理")
@RestController
@RequestMapping("/code/execution")
@Validated
public class CodeExecutionController extends BaseController {
    @Resource
    private IDockerExecutionService dockerExecutionService;

    @Resource
    private DockerConfig dockerConfig;

    /**
     * 执行代码（同步）
     */
    @ApiOperation(value = "执行代码", notes = "同步执行代码并返回结果")
    @Log(title = "代码执行", businessType = BusinessType.OTHER)
    @PostMapping("/execute")
    public AjaxResult executeCode(@Valid @RequestBody CodeExecution codeExecution) {
        try {
            Long userId = SecurityUtils.getUserId();
            log.info("收到代码执行请求，语言: {}, 用户: {}", codeExecution.getLanguage(), userId);

            // 检查Docker服务状态
            if (!dockerExecutionService.isDockerAvailable()) {
                log.error("Docker服务不可用，请联系管理员");
                dockerConfig.reconnectDockerClient();
                return error("虚拟容器服务不可用，请联系管理员");
            }

            // 执行代码
            ExecutionResult result = dockerExecutionService.executeCode(codeExecution);

            log.info("代码执行完成，执行ID: {}, 状态: {}", result.getExecutionId(), result.getStatus());

            return success(result);

        } catch (Exception e) {
            log.error("代码执行失败: {}", e.getMessage(), e);
            return error("代码执行失败: " + e.getMessage());
        }
    }

    /**
     * 执行上传的代码文件
     */
    @ApiOperation(value = "执行上传的代码文件", notes = "上传代码文件并执行")
    @Log(title = "文件代码执行", businessType = BusinessType.OTHER)
    @PostMapping("/execute/file")
    public AjaxResult executeCodeFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "language", defaultValue = "python") String language,
            @RequestParam(value = "input", required = false) String input,
            @RequestParam(value = "timeoutSeconds", defaultValue = "30") Integer timeoutSeconds,
            @RequestParam(value = "memoryLimitMB", defaultValue = "128") Integer memoryLimitMB,
            @RequestParam(value = "cpuLimit", defaultValue = "1") Double cpuLimit,
            @RequestParam(value = "userId", required = false) String userId) {

        try {
            log.info("收到文件代码执行请求，文件名: {}, 语言: {}, 用户: {}",
                    file.getOriginalFilename(), language, userId);

            // 检查Docker服务状态
            if (!dockerExecutionService.isDockerAvailable()) {
                dockerConfig.reconnectDockerClient();
                return error("虚拟容器服务不可用，请联系管理员");
            }

            // 保存上传的文件到临时目录
            String tempFilePath = saveUploadedFile(file);

            // 构建执行请求
            CodeExecution request = new CodeExecution()
                    .setCodeFilePath(tempFilePath)
                    .setCodeFileName(file.getOriginalFilename())
                    .setLanguage(language)
                    .setInput(input)
                    .setTimeoutSeconds(timeoutSeconds)
                    .setMemoryLimitMB(memoryLimitMB)
                    .setCpuLimit(cpuLimit)
                    .setUserId(userId)
                    .setDescription("文件上传执行: " + file.getOriginalFilename());

            // 执行代码
            ExecutionResult result = dockerExecutionService.executeCode(request);

            // 清理临时文件
            cleanupTempFile(tempFilePath);

            log.info("文件代码执行完成，执行ID: {}, 状态: {}", result.getExecutionId(), result.getStatus());

            return success(result);

        } catch (Exception e) {
            log.error("文件代码执行失败: {}", e.getMessage(), e);
            return error("文件代码执行失败: " + e.getMessage());
        }
    }

    /**
     * 异步执行代码
     */
    @ApiOperation(value = "异步执行代码", notes = "异步执行代码，立即返回执行ID")
    @Log(title = "异步代码执行", businessType = BusinessType.OTHER)
    @PostMapping("/execute/async")
    public AjaxResult executeCodeAsync(@Valid @RequestBody CodeExecution request) {
        try {
            log.info("收到异步代码执行请求，语言: {}, 用户: {}", request.getLanguage(), request.getUserId());

            // 检查Docker服务状态
            if (!dockerExecutionService.isDockerAvailable()) {
                return error("Docker服务不可用，请联系管理员");
            }

            // 异步执行代码
            String executionId = dockerExecutionService.executeCodeAsync(request);

            log.info("异步代码执行已启动，执行ID: {}", executionId);

            return success(executionId);

        } catch (Exception e) {
            log.error("异步代码执行失败: {}", e.getMessage(), e);
            return error("异步代码执行失败: " + e.getMessage());
        }
    }

    /**
     * 获取执行结果
     */
    @ApiOperation(value = "获取执行结果", notes = "根据执行ID获取代码执行结果")
    @GetMapping("/result/{executionId}")
    public AjaxResult getExecutionResult(
            @ApiParam(value = "执行ID", required = true)
            @PathVariable String executionId) {
        try {
            ExecutionResult result = dockerExecutionService.getExecutionResult(executionId);

            if (result == null) {
                return error("未找到执行记录: " + executionId);
            }

            return success(result);

        } catch (Exception e) {
            log.error("获取执行结果失败: {}", e.getMessage(), e);
            return error("获取执行结果失败: " + e.getMessage());
        }
    }

    /**
     * 停止代码执行
     */
    @ApiOperation(value = "停止代码执行", notes = "强制停止正在执行的代码")
    @Log(title = "停止代码执行", businessType = BusinessType.OTHER)
    @PostMapping("/stop/{executionId}")
    public AjaxResult stopExecution(
            @ApiParam(value = "执行ID", required = true)
            @PathVariable String executionId) {
        try {
            boolean success = dockerExecutionService.stopExecution(executionId);

            if (success) {
                log.info("代码执行已停止，执行ID: {}", executionId);
                return success("代码执行已停止");
            } else {
                return error("停止代码执行失败，可能执行已完成或不存在");
            }

        } catch (Exception e) {
            log.error("停止代码执行失败: {}", e.getMessage(), e);
            return error("停止代码执行失败: " + e.getMessage());
        }
    }

    /**
     * 获取正在运行的执行任务
     */
    @ApiOperation(value = "获取运行中的任务", notes = "获取所有正在运行的代码执行任务")
    @GetMapping("/running")
    public AjaxResult getRunningExecutions() {
        try {
            List<ExecutionResult> runningExecutions = dockerExecutionService.getRunningExecutions();
            return success(runningExecutions);

        } catch (Exception e) {
            log.error("获取运行中任务失败: {}", e.getMessage(), e);
            return error("获取运行中任务失败: " + e.getMessage());
        }
    }

    /**
     * 清理过期容器
     */
    @ApiOperation(value = "清理过期容器", notes = "手动清理超过生命周期的容器")
    @Log(title = "清理过期容器", businessType = BusinessType.OTHER)
    @PostMapping("/cleanup")
    public AjaxResult cleanupExpiredContainers() {
        try {
            int cleanedCount = dockerExecutionService.cleanupExpiredContainers();
            log.info("清理过期容器完成，清理数量: {}", cleanedCount);
            return success("清理完成，清理了 " + cleanedCount + " 个过期容器");

        } catch (Exception e) {
            log.error("清理过期容器失败: {}", e.getMessage(), e);
            return error("清理过期容器失败: " + e.getMessage());
        }
    }

    /**
     * 获取可用的Python包列表
     */
    @ApiOperation(value = "获取Python包列表", notes = "获取支持安装的Python包列表")
    @GetMapping("/python/packages")
    public AjaxResult getAvailablePythonPackages() {
        try {
            List<String> packages = dockerExecutionService.getAvailablePythonPackages();
            return success(packages);

        } catch (Exception e) {
            log.error("获取Python包列表失败: {}", e.getMessage(), e);
            return error("获取Python包列表失败: " + e.getMessage());
        }
    }

    /**
     * 检查Docker服务状态
     */
    @ApiOperation(value = "检查Docker状态", notes = "检查Docker服务是否可用")
    @GetMapping("/docker/status")
    public AjaxResult checkDockerStatus() {
        try {
            return success(dockerExecutionService.isDockerAvailable());
        } catch (Exception e) {
            log.error("检查Docker状态失败: {}", e.getMessage(), e);
            return error("检查Docker状态失败: " + e.getMessage());
        }
    }

    /**
     * 代码执行示例
     */
    @ApiOperation(value = "获取代码示例", notes = "获取各种语言的代码执行示例")
    @GetMapping("/examples")
    public AjaxResult getCodeExamples() {
        try {
            // Python示例
            String pythonExample = "# Python代码示例\n" +
                    "import math\n" +
                    "import datetime\n\n" +
                    "# 计算圆的面积\n" +
                    "radius = 5\n" +
                    "area = math.pi * radius ** 2\n" +
                    "print(f'圆的半径: {radius}')\n" +
                    "print(f'圆的面积: {area:.2f}')\n\n" +
                    "# 当前时间\n" +
                    "now = datetime.datetime.now()\n" +
                    "print(f'当前时间: {now.strftime(\"%Y-%m-%d %H:%M:%S\")}')\n\n" +
                    "# 简单的数据处理\n" +
                    "numbers = [1, 2, 3, 4, 5]\n" +
                    "squared = [x**2 for x in numbers]\n" +
                    "print(f'原数组: {numbers}')\n" +
                    "print(f'平方后: {squared}')";

            Map<String, String> examples = new HashMap<>();
            examples.put("python", pythonExample);
            return success(examples);

        } catch (Exception e) {
            log.error("获取代码示例失败: {}", e.getMessage(), e);
            return error("获取代码示例失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前用户的容器信息
     */
    @ApiOperation(value = "获取用户容器", notes = "获取当前用户的专属容器信息")
    @GetMapping("/user/container")
    public AjaxResult getUserContainer() {
        try {
            Long userId = SecurityUtils.getUserId();
            String containerId = dockerExecutionService.getUserContainerId(userId);

            if (containerId != null) {
                Map<String, Object> containerInfo = new HashMap<>();
                containerInfo.put("userId", userId);
                containerInfo.put("containerId", containerId);
                containerInfo.put("status", "running");
                return success( containerInfo);
            } else {
                return success( null);
            }

        } catch (Exception e) {
            log.error("获取用户容器信息失败: {}", e.getMessage(), e);
            return error("获取用户容器信息失败: " + e.getMessage());
        }
    }

    /**
     * 删除当前用户的容器
     */
    @ApiOperation(value = "删除用户容器", notes = "删除当前用户的专属容器")
    @Log(title = "删除用户容器", businessType = BusinessType.DELETE)
    @DeleteMapping("/user/container")
    public AjaxResult removeUserContainer() {
        try {
            Long userId = SecurityUtils.getUserId();
            boolean success = dockerExecutionService.removeUserContainer(userId);

            if (success) {
                log.info("用户 {} 的容器已删除", userId);
                return success("用户容器删除成功");
            } else {
                return error("用户容器删除失败，可能容器不存在");
            }

        } catch (Exception e) {
            log.error("删除用户容器失败: {}", e.getMessage(), e);
            return error("删除用户容器失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有用户容器状态（管理员功能）
     */
    @ApiOperation(value = "获取所有用户容器", notes = "获取所有用户的容器状态信息")
    @GetMapping("/admin/containers")
    public AjaxResult getAllUserContainers() {
        try {
            Map<Long, String> userContainers = dockerExecutionService.getAllUserContainers();

            List<Map<String, Object>> containerList = new ArrayList<>();
            for (Map.Entry<Long, String> entry : userContainers.entrySet()) {
                Map<String, Object> containerInfo = new HashMap<>();
                containerInfo.put("userId", entry.getKey());
                containerInfo.put("containerId", entry.getValue());
                containerInfo.put("status", "running");
                containerList.add(containerInfo);
            }

            return success(containerList);

        } catch (Exception e) {
            log.error("获取所有用户容器失败: {}", e.getMessage(), e);
            return error("获取所有用户容器失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户容器日志
     */
    @ApiOperation(value = "获取容器日志", notes = "获取当前用户容器的运行日志")
    @GetMapping("/user/container/logs")
    public AjaxResult getUserContainerLogs(
            @ApiParam(value = "日志行数", example = "100")
            @RequestParam(value = "lines", defaultValue = "100") Integer lines) {
        try {
            Long userId = SecurityUtils.getUserId();
            String containerId = dockerExecutionService.getUserContainerId(userId);

            if (containerId == null) {
                return error("用户暂无专属容器");
            }

            String logs = dockerExecutionService.getContainerLogs(containerId, lines);

            Map<String, Object> result = new HashMap<>();
            result.put("containerId", containerId);
            result.put("userId", userId);
            result.put("lines", lines);
            result.put("logs", logs);

            return success("获取容器日志成功", result);

        } catch (Exception e) {
            log.error("获取容器日志失败: {}", e.getMessage(), e);
            return error("获取容器日志失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定容器日志（管理员功能）
     */
    @ApiOperation(value = "获取指定容器日志", notes = "管理员获取指定容器的运行日志")
    @GetMapping("/admin/container/{containerId}/logs")
    public AjaxResult getContainerLogs(
            @ApiParam(value = "容器ID", required = true)
            @PathVariable String containerId,
            @ApiParam(value = "日志行数", example = "100")
            @RequestParam(value = "lines", defaultValue = "100") Integer lines) {
        try {
            String logs = dockerExecutionService.getContainerLogs(containerId, lines);

            Map<String, Object> result = new HashMap<>();
            result.put("containerId", containerId);
            result.put("lines", lines);
            result.put("logs", logs);

            return success("获取容器日志成功", result);

        } catch (Exception e) {
            log.error("获取容器日志失败: {}", e.getMessage(), e);
            return error("获取容器日志失败: " + e.getMessage());
        }
    }

    /**
     * 保存上传的文件到临时目录
     */
    private String saveUploadedFile(MultipartFile file) throws IOException {
        // 使用配置的临时目录
        String tempDir = dockerConfig.getComputedLocalTempDir();
        Path tempDirPath = Paths.get(tempDir);
        if (!Files.exists(tempDirPath)) {
            Files.createDirectories(tempDirPath);
        }

        // 生成唯一文件名
        String fileName = System.currentTimeMillis() + "_" + file.getOriginalFilename();
        Path tempFilePath = tempDirPath.resolve(fileName);

        // 保存文件
        Files.copy(file.getInputStream(), tempFilePath);

        log.debug("文件已保存到临时目录: {}", tempFilePath.toString());
        return tempFilePath.toString();
    }

    /**
     * 清理临时文件
     */
    private void cleanupTempFile(String filePath) {
        try {
            Path path = Paths.get(filePath);
            if (Files.exists(path)) {
                Files.delete(path);
                log.debug("临时文件已清理: {}", filePath);
            }
        } catch (Exception e) {
            log.warn("清理临时文件失败: {}, 错误: {}", filePath, e.getMessage());
        }
    }
}

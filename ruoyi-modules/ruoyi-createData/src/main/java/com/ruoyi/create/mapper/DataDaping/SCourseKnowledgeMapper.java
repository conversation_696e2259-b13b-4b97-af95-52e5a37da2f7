package com.ruoyi.create.mapper.DataDaping;

import com.ruoyi.create.domain.DataDaping.SCourseKnowledge;

import java.util.List;


/**
 * 知识库课程数量Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-08
 */
public interface SCourseKnowledgeMapper 
{

    List<SCourseKnowledge> selectLatestSCourseKnowledge();

    /**
     * 查询知识库课程数量
     * 
     * @param id 知识库课程数量主键
     * @return 知识库课程数量
     */
    public SCourseKnowledge selectSCourseKnowledgeById(Long id);

    /**
     * 查询知识库课程数量列表
     * 
     * @param sCourseKnowledge 知识库课程数量
     * @return 知识库课程数量集合
     */
    public List<SCourseKnowledge> selectSCourseKnowledgeList(SCourseKnowledge sCourseKnowledge);

    /**
     * 新增知识库课程数量
     * 
     * @param sCourseKnowledge 知识库课程数量
     * @return 结果
     */
    public int insertSCourseKnowledge(SCourseKnowledge sCourseKnowledge);

    /**
     * 修改知识库课程数量
     * 
     * @param sCourseKnowledge 知识库课程数量
     * @return 结果
     */
    public int updateSCourseKnowledge(SCourseKnowledge sCourseKnowledge);

    /**
     * 删除知识库课程数量
     * 
     * @param id 知识库课程数量主键
     * @return 结果
     */
    public int deleteSCourseKnowledgeById(Long id);

    /**
     * 批量删除知识库课程数量
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSCourseKnowledgeByIds(Long[] ids);
}

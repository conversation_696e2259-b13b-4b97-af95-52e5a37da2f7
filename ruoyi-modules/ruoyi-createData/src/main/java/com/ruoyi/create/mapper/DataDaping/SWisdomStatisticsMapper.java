package com.ruoyi.create.mapper.DataDaping;

import com.ruoyi.create.domain.DataDaping.SWisdomStatistics;

import java.util.List;


/**
 * 智慧学堂统计Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-08
 */
public interface SWisdomStatisticsMapper 
{
    SWisdomStatistics selectLatestWisdomStatistics();

    /**
     * 查询智慧学堂统计
     * 
     * @param id 智慧学堂统计主键
     * @return 智慧学堂统计
     */
    public SWisdomStatistics selectSWisdomStatisticsById(Long id);

    /**
     * 查询智慧学堂统计列表
     * 
     * @param sWisdomStatistics 智慧学堂统计
     * @return 智慧学堂统计集合
     */
    public List<SWisdomStatistics> selectSWisdomStatisticsList(SWisdomStatistics sWisdomStatistics);

    /**
     * 新增智慧学堂统计
     * 
     * @param sWisdomStatistics 智慧学堂统计
     * @return 结果
     */
    public int insertSWisdomStatistics(SWisdomStatistics sWisdomStatistics);

    /**
     * 修改智慧学堂统计
     * 
     * @param sWisdomStatistics 智慧学堂统计
     * @return 结果
     */
    public int updateSWisdomStatistics(SWisdomStatistics sWisdomStatistics);

    /**
     * 删除智慧学堂统计
     * 
     * @param id 智慧学堂统计主键
     * @return 结果
     */
    public int deleteSWisdomStatisticsById(Long id);

    /**
     * 批量删除智慧学堂统计
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSWisdomStatisticsByIds(Long[] ids);
}

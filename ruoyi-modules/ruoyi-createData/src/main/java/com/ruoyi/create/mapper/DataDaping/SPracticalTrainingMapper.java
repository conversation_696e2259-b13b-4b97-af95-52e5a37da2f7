package com.ruoyi.create.mapper.DataDaping;

import com.ruoyi.create.domain.DataDaping.SPracticalTraining;

import java.util.List;


/**
 * 实践实训Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-08
 */
public interface SPracticalTrainingMapper 
{
    List<SPracticalTraining> selectLatestPracticalTrainingList();

    /**
     * 查询实践实训
     * 
     * @param id 实践实训主键
     * @return 实践实训
     */
    public SPracticalTraining selectSPracticalTrainingById(Long id);

    /**
     * 查询实践实训列表
     * 
     * @param sPracticalTraining 实践实训
     * @return 实践实训集合
     */
    public List<SPracticalTraining> selectSPracticalTrainingList(SPracticalTraining sPracticalTraining);

    /**
     * 新增实践实训
     * 
     * @param sPracticalTraining 实践实训
     * @return 结果
     */
    public int insertSPracticalTraining(SPracticalTraining sPracticalTraining);

    /**
     * 修改实践实训
     * 
     * @param sPracticalTraining 实践实训
     * @return 结果
     */
    public int updateSPracticalTraining(SPracticalTraining sPracticalTraining);

    /**
     * 删除实践实训
     * 
     * @param id 实践实训主键
     * @return 结果
     */
    public int deleteSPracticalTrainingById(Long id);

    /**
     * 批量删除实践实训
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSPracticalTrainingByIds(Long[] ids);
}

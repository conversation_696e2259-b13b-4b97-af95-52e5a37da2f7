package com.ruoyi.create.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.time.LocalDateTime;

/** 应用场景知识库信息表 */
@Data
@TableName("application_scenario_knowledge_base")
public class ApplicationScenarioKnowledgeBase {

    /** 主键自增 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 知识库 ID */
    private String kbId;

    /** 知识库名称 */
    private String kbName;

    /** 是否使用自定义处理策略（0/1） */
    private Boolean isCustomProcessRule;

    /** 自定义处理策略 JSON */
    private String customProcessRule;

    /** 是否开启知识增强（0/1） */
    private Boolean isEnhanced;

    /** 应用场景 ID */
    private String applicationScenarioId;

    /** 创建人 / 修改人 */
    private String createBy;
    private String updateBy;

    /** 创建时间 / 修改时间 */
    private LocalDateTime createTime;
    private LocalDateTime updateTime;

    /** 菜单路由 */
    private String menuRouting;
}

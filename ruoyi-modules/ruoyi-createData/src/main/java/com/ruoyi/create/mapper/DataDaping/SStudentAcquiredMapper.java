package com.ruoyi.create.mapper.DataDaping;

import com.ruoyi.create.domain.DataDaping.SStudentAcquired;

import java.util.List;


/**
 * 学生专业课程知识点掌握数量Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-08
 */
public interface SStudentAcquiredMapper 
{
    List<SStudentAcquired> selectLatestStudentAcquiredList();

    /**
     * 查询学生专业课程知识点掌握数量
     * 
     * @param id 学生专业课程知识点掌握数量主键
     * @return 学生专业课程知识点掌握数量
     */
    public SStudentAcquired selectSStudentAcquiredById(Long id);

    /**
     * 查询学生专业课程知识点掌握数量列表
     * 
     * @param sStudentAcquired 学生专业课程知识点掌握数量
     * @return 学生专业课程知识点掌握数量集合
     */
    public List<SStudentAcquired> selectSStudentAcquiredList(SStudentAcquired sStudentAcquired);

    /**
     * 新增学生专业课程知识点掌握数量
     * 
     * @param sStudentAcquired 学生专业课程知识点掌握数量
     * @return 结果
     */
    public int insertSStudentAcquired(SStudentAcquired sStudentAcquired);

    /**
     * 修改学生专业课程知识点掌握数量
     * 
     * @param sStudentAcquired 学生专业课程知识点掌握数量
     * @return 结果
     */
    public int updateSStudentAcquired(SStudentAcquired sStudentAcquired);

    /**
     * 删除学生专业课程知识点掌握数量
     * 
     * @param id 学生专业课程知识点掌握数量主键
     * @return 结果
     */
    public int deleteSStudentAcquiredById(Long id);

    /**
     * 批量删除学生专业课程知识点掌握数量
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSStudentAcquiredByIds(Long[] ids);
}

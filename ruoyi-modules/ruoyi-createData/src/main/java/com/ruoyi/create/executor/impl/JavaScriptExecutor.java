package com.ruoyi.create.executor.impl;

import com.ruoyi.create.executor.AbstractLanguageExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * JavaScript代码执行器
 *
 * <AUTHOR>
 * @date 2024-07-31
 */
@Slf4j
@Component
public class JavaScriptExecutor extends AbstractLanguageExecutor {

    @Override
    public String getSupportedLanguage() {
        return "javascript";
    }

    @Override
    public String getDockerImage() {
        return "multi-lang-executor:latest";
    }

    @Override
    public String getFileExtension() {
        return ".js";
    }

    @Override
    public String getDefaultFileName() {
        return "main.js";
    }

    @Override
    public String[] getExecuteCommand(String codeFilePath, String input) {
        return new String[]{"node", codeFilePath};
    }

    @Override
    public void installPackages(String containerId, 
                               List<String> packages,
                               com.ruoyi.create.service.impl.DockerExecutionServiceImpl executionService) {
        if (packages == null || packages.isEmpty()) {
            return;
        }

        for (String packageName : packages) {
            try {
                log.info("正在安装Node.js包: {}", packageName);
                String installCmd = String.format(
                    "echo \"[$(date '+%%Y-%%m-%%d %%H:%%M:%%S')] 开始安装Node.js包: %s\" | tee /proc/1/fd/1 && " +
                    "npm install %s && " +
                    "echo \"[$(date '+%%Y-%%m-%%d %%H:%%M:%%S')] Node.js包安装成功: %s\" | tee /proc/1/fd/1",
                    packageName, packageName, packageName
                );
                executionService.execCommandInContainer(containerId, new String[]{"sh", "-c", installCmd});
                log.info("Node.js包安装成功: {}", packageName);
            } catch (Exception e) {
                log.warn("Node.js包安装失败: {}, 错误: {}", packageName, e.getMessage());
                // 继续安装其他包，不中断流程
            }
        }
    }

    @Override
    public String validateSyntax(String code) {
        if (code == null || code.trim().isEmpty()) {
            return "代码内容不能为空";
        }

        // 检查基本的JavaScript语法错误
        int braceCount = 0;
        int parenCount = 0;
        int bracketCount = 0;
        
        for (char c : code.toCharArray()) {
            if (c == '{') braceCount++;
            if (c == '}') braceCount--;
            if (c == '(') parenCount++;
            if (c == ')') parenCount--;
            if (c == '[') bracketCount++;
            if (c == ']') bracketCount--;
            
            if (braceCount < 0) {
                return "大括号不匹配：多余的右大括号";
            }
            if (parenCount < 0) {
                return "小括号不匹配：多余的右小括号";
            }
            if (bracketCount < 0) {
                return "中括号不匹配：多余的右中括号";
            }
        }
        
        if (braceCount != 0) {
            return "大括号不匹配：缺少" + (braceCount > 0 ? "右" : "左") + "大括号";
        }
        
        if (parenCount != 0) {
            return "小括号不匹配：缺少" + (parenCount > 0 ? "右" : "左") + "小括号";
        }
        
        if (bracketCount != 0) {
            return "中括号不匹配：缺少" + (bracketCount > 0 ? "右" : "左") + "中括号";
        }

        // 检查常见的语法错误
        if (code.contains("console.log(") && !code.contains(")")) {
            return "console.log语句语法错误：缺少右括号";
        }

        return null; // 验证通过
    }
}

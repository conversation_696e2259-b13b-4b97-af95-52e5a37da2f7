# Redis配置示例 - 解决缓存乱码问题
# 本地开发环境使用单机模式，线上环境使用主从模式

spring:
  redis:
    # Redis模式：single(单机) 或 sentinel(主从)
    mode: single
    
    # 单机模式配置（本地开发）
    host: localhost
    port: 6379
    password: 
    database: 0
    timeout: 3000
    
    # 主从模式配置（线上环境）
    # 当mode=sentinel时生效
    sentinel:
      master: mymaster
      nodes: 
        - *************:26379
        - *************:26379
        - *************:26379
    
    # 连接池配置
    lettuce:
      pool:
        max-active: 10
        max-idle: 8
        min-idle: 2
        max-wait: -1ms
      shutdown-timeout: 100ms

---
# 线上环境配置示例
spring:
  profiles: prod
  redis:
    mode: sentinel
    password: your_redis_password
    sentinel:
      master: mymaster
      nodes: 
        - redis-sentinel-1:26379
        - redis-sentinel-2:26379
        - redis-sentinel-3:26379

---
# 开发环境配置示例
spring:
  profiles: dev
  redis:
    mode: single
    host: localhost
    port: 6379
    password: 
    database: 0

# Redis缓存乱码解决方案

## 问题描述

在使用Redisson进行Redis缓存时，发现存储的数据在Redis客户端中显示为乱码，无法直接观察数据内容，影响调试和监控。

## 问题原因

1. **默认序列化器问题**：Redisson默认使用Java序列化，产生二进制数据
2. **缺少统一配置**：每个RMap都需要单独指定序列化器，代码重复
3. **中文字符支持**：默认序列化器对中文字符支持不佳
4. **时间类型序列化**：LocalDateTime等Java 8时间类型需要特殊处理

## 解决方案

### 1. 统一配置JSON序列化器

创建`RedissonConfig`配置类，全局设置JSON序列化器：

```java
@Configuration
public class RedissonConfig {
    
    @Bean
    public ObjectMapper redisObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        // 注册Java 8时间模块
        objectMapper.registerModule(new JavaTimeModule());
        // 设置可见性
        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        // 启用类型信息
        objectMapper.activateDefaultTyping(
            LaissezFaireSubTypeValidator.instance,
            ObjectMapper.DefaultTyping.NON_FINAL,
            JsonTypeInfo.As.PROPERTY
        );
        return objectMapper;
    }
    
    @Bean
    public JsonJacksonCodec jsonJacksonCodec() {
        return new JsonJacksonCodec(redisObjectMapper());
    }
    
    @Bean
    @Primary
    public RedissonClient redissonClient() {
        Config config = new Config();
        // 配置Redis连接...
        
        // 设置全局JSON编解码器 - 关键配置
        config.setCodec(jsonJacksonCodec());
        
        return Redisson.create(config);
    }
}
```

### 2. 支持本地单机和线上主从模式

```yaml
spring:
  redis:
    # Redis模式：single(单机) 或 sentinel(主从)
    mode: single
    
    # 单机模式配置（本地开发）
    host: localhost
    port: 6379
    password: 
    database: 0
    
    # 主从模式配置（线上环境）
    sentinel:
      master: mymaster
      nodes: 
        - redis-sentinel-1:26379
        - redis-sentinel-2:26379
        - redis-sentinel-3:26379
```

### 3. 简化业务代码

配置完成后，业务代码无需重复指定序列化器：

```java
// 之前：需要为每个Map指定序列化器
executionResults = redissonClient.getMap("results", 
    new TypedJsonJacksonCodec(String.class, ExecutionResult.class));

// 现在：直接使用，自动应用全局序列化器
executionResults = redissonClient.getMap("results");
```

### 4. 实体类优化

为实体类添加JSON序列化注解：

```java
@Data
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ExecutionResult implements Serializable {
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
    
    // 其他字段...
}
```

## 效果对比

### 解决前（乱码）
```
127.0.0.1:6379> hgetall docker:execution:results
1) "exec-123"
2) "\xac\xed\x00\x05sr\x00\x1ecom.ruoyi.create.domain..."
```

### 解决后（可读）
```
127.0.0.1:6379> hgetall docker:execution:results
1) "exec-123"
2) "{\"@class\":\"com.ruoyi.create.domain.ExecutionResult\",\"executionId\":\"exec-123\",\"status\":\"SUCCESS\",\"stdout\":\"Hello World!\",\"startTime\":\"2024-08-05 10:30:00\"}"
```

## 依赖配置

确保项目包含必要的依赖：

```xml
<!-- Redisson -->
<dependency>
    <groupId>org.redisson</groupId>
    <artifactId>redisson-spring-boot-starter</artifactId>
    <version>3.17.7</version>
</dependency>

<!-- Jackson JSON处理 -->
<dependency>
    <groupId>com.fasterxml.jackson.core</groupId>
    <artifactId>jackson-databind</artifactId>
    <version>2.13.1</version>
</dependency>

<!-- Jackson Java 8时间模块 -->
<dependency>
    <groupId>com.fasterxml.jackson.datatype</groupId>
    <artifactId>jackson-datatype-jsr310</artifactId>
    <version>2.13.1</version>
</dependency>
```

## 测试验证

系统启动后会自动进行序列化测试：

1. **ExecutionResult对象序列化测试**
2. **List<String>集合序列化测试**
3. **LocalDateTime时间序列化测试**

查看日志确认测试结果：
```
Redis JSON序列化测试成功 - 数据可正常读写，中文支持正常
Redis List序列化测试成功 - 中文容器名支持正常
Redis LocalDateTime序列化测试成功 - 时间精度保持正常
```

## 注意事项

1. **性能影响**：JSON序列化比Java序列化稍慢，但可读性大大提升
2. **存储空间**：JSON格式占用空间略大，但便于调试和监控
3. **版本兼容**：确保Jackson版本与Spring Boot版本兼容
4. **类型信息**：启用了类型信息，确保复杂对象正确反序列化

## 监控建议

1. 使用Redis客户端直接查看数据内容
2. 通过日志监控序列化测试结果
3. 定期检查缓存数据的完整性
4. 监控Redis内存使用情况

通过以上配置，Redis缓存数据将以可读的JSON格式存储，彻底解决乱码问题，提升开发和运维效率。
